<!DOCTYPE html><html lang="zh-CN"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>异常调度系统的工作流与运维规则测试</title>

<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://picture-search.tiangong.cn/tailwindcss.com"></script><style>
        body {width: 1280px; height: 720px; overflow: hidden; margin: 0;
            padding: 0;}
        .slide {
            width: 1280px;
            height: 720px;
            position: relative;
            background-color: #F5F5F5;

        }
        .geometric-shape {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(135deg, #FFA500, #FF6347);
            opacity: 0.8;
            z-index: 1;
        }
        .title-text {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        .highlight {
            background-color: #E6F3FF;
            padding: 2px 8px;
            border-radius: 4px;
        }

html {
    overflow: hidden;
}</style>
</head>
<body>
<div class="slide flex flex-col items-center justify-center relative" style=" overflow: hidden;">
<!-- Decorative geometric shapes -->
<div class="geometric-shape w-64 h-64 top-[-100px] right-[-50px]"></div>
<div class="geometric-shape w-96 h-96 bottom-[-150px] left-[-100px]"></div>
<div class="geometric-shape w-40 h-40 top-[100px] left-[100px] opacity-60"></div>
<!-- Cover content container -->
<div class="z-10 text-center px-16 max-w-4xl">
<!-- Small header -->
<div class="mb-4">
<span class="bg-orange-500 text-white px-4 py-1 rounded-full text-sm font-medium">智能助手小蜜</span>
</div>
<!-- Main title -->
<h1 class="title-text text-5xl font-bold text-gray-800 mb-6">
                异常调度系统的工作流与运维规则<span class="text-orange-500">测试</span>
</h1>
<!-- Subtitle -->
<p class="text-2xl text-gray-700 mb-10">
                通过<span class="highlight">智能对话交互</span>提升测试效率与用户体验
            </p>
<!-- Feature highlights -->
<div class="flex justify-center space-x-12 mb-12">
<div class="flex flex-col items-center">
<i class="fas fa-robot text-3xl text-orange-500 mb-2"></i>
<span class="text-gray-700 font-medium">引导式参数配置</span>
</div>
<div class="flex flex-col items-center">
<i class="fas fa-tachometer-alt text-3xl text-orange-500 mb-2"></i>
<span class="text-gray-700 font-medium">即时任务反馈</span>
</div>
<div class="flex flex-col items-center">
<i class="fas fa-search text-3xl text-orange-500 mb-2"></i>
<span class="text-gray-700 font-medium">智能资源查询</span>
</div>
</div>
<!-- Date -->
<div class="mt-10 text-gray-500">
<i class="far fa-calendar-alt mr-2"></i>2025-08-28
            </div>
</div>
<!-- Bottom decorative bar -->
<div class="absolute bottom-0 w-full h-8 bg-gradient-to-r from-orange-500 to-orange-600"></div>
</div>

</body></html>