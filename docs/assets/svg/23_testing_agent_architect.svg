<?xml version="1.0" encoding="UTF-8"?>
<svg width="1700" height="1350" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <!-- Enhanced Gradients inspired by reference design -->
        <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#3b82f6;"/>
            <stop offset="100%" style="stop-color:#1d4ed8;"/>
        </linearGradient>
        <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:#f8fafc;"/>
            <stop offset="100%" style="stop-color:#f1f5f9;"/>
        </linearGradient>
        <linearGradient id="pastGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#ef4444;"/>
            <stop offset="100%" style="stop-color:#dc2626;"/>
        </linearGradient>
        <linearGradient id="presentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#10b981;"/>
            <stop offset="100%" style="stop-color:#059669;"/>
        </linearGradient>
        <linearGradient id="agentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#8b5cf6;"/>
            <stop offset="100%" style="stop-color:#7c3aed;"/>
        </linearGradient>
        <linearGradient id="techGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#64748b;"/>
            <stop offset="100%" style="stop-color:#475569;"/>
        </linearGradient>
        <linearGradient id="toolsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#0ea5e9;"/>
            <stop offset="100%" style="stop-color:#0284c7;"/>
        </linearGradient>

        <!-- Enhanced Filters -->
        <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="0" dy="5" stdDeviation="10" flood-color="#1e293b" flood-opacity="0.1"/>
        </filter>
        <filter id="subtleShadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#1e293b" flood-opacity="0.06"/>
        </filter>

        <!-- Enhanced Markers -->
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#475569"/>
        </marker>

        <!-- Enhanced Styles -->
        <style>
            .font-sans { font-family: 'Inter', 'Segoe UI', sans-serif; }
            .title { font-size: 32px; font-weight: 700; fill: white; text-anchor: middle; }
            .subtitle { font-size: 18px; fill: white; opacity: 0.9; text-anchor: middle; font-weight: 500; letter-spacing: 0.5px; }
            .section-title { font-size: 24px; font-weight: 600; fill: #1e293b; text-anchor: middle; }
            .card-title { font-size: 20px; font-weight: 600; fill: white; }
            .card-subtitle { font-size: 15px; font-weight: 500; fill: white; opacity: 0.9; }
            .card-desc { font-size: 16px; fill: #e5e7eb; opacity: 0.9; }
            .flow-label { font-size: 14px; font-weight: 500; fill: #334155; text-anchor: middle; }
            .tech-title { font-size: 18px; font-weight: 600; fill: #ffffff; }
            .tech-desc { font-size: 15px; fill: #f0f0f0; opacity: 0.95; }
            .react-step { font-size: 15px; font-weight: 600; fill: white; text-anchor: middle; opacity: 0.8; }
            .react-desc { font-size: 13px; fill: #e0e0e0; text-anchor: middle; opacity: 0.9; }
            .code { font-family: 'Fira Code', 'Courier New', monospace; font-size: 14px; fill: #fde047; }
            .highlight-text { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 16px; font-weight: 600; fill: #f97316; }
            .marker-circle { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 14px; font-weight: 700; stroke: rgba(255,255,255,0.7); stroke-width: 2px; }
        </style>
    </defs>

    <!-- Background -->
    <rect width="1700" height="1350" fill="url(#bgGradient)"/>

    <!-- Header -->
    <rect x="0" y="0" width="1700" height="90" fill="url(#headerGradient)" filter="url(#dropShadow)"/>
    <text x="850" y="50" class="font-sans title">智能测试Agent: 架构与核心能力</text>
    <text x="850" y="75" class="font-sans subtitle">从繁琐填表到智能对话的测试体验革命</text>

    <!-- User Story with Enhanced Design -->
    <text x="850" y="140" class="font-sans section-title">测试体验对比：过去 vs 现在</text>
    <g id="user-story">
        <!-- Past Experience -->
        <rect x="150" y="170" width="650" height="180" rx="16" fill="url(#pastGradient)" filter="url(#dropShadow)"/>
        <text x="180" y="210" class="font-sans card-title">❌ 过去: 繁琐、易错的"填表式"测试</text>
        <g transform="translate(180, 240)">
            <circle cx="15" cy="0" r="12" fill="#f97316"/>
            <text x="15" y="5" text-anchor="middle" fill="white" class="marker-circle">1</text>
            <text x="40" y="5" class="font-sans card-desc">信息过载: 为了找到正确的测试用例和集群参数，<tspan x="40" dy="1.2em">我需要到处翻文档、问同事</tspan></text>
        </g>
        <g transform="translate(180, 285)">
            <circle cx="15" cy="0" r="12" fill="#f97316"/>
            <text x="15" y="5" text-anchor="middle" fill="white" class="marker-circle">2</text>
            <text x="40" y="5" class="font-sans card-desc">僵化流程: 静态表单缺乏引导，一旦填错任何一项，<tspan x="40" dy="1.2em">就得从头再来，非常沮丧</tspan></text>
        </g>
        <g transform="translate(180, 330)">
            <circle cx="15" cy="0" r="12" fill="#f97316"/>
            <text x="15" y="5" text-anchor="middle" fill="white" class="marker-circle">3</text>
            <text x="40" y="5" class="font-sans card-desc">上下文切换: 我必须在多个工具和界面之间来回跳转，<tspan x="40" dy="1.2em">打断了我的思路</tspan></text>
        </g>

        <!-- Present Experience -->
        <rect x="900" y="170" width="650" height="180" rx="16" fill="url(#presentGradient)" filter="url(#dropShadow)"/>
        <text x="930" y="210" class="font-sans card-title">✅ 现在: 流畅、智能的"对话式"测试</text>
        <g transform="translate(930, 240)">
            <circle cx="15" cy="0" r="12" fill="#f97316"/>
            <text x="15" y="5" text-anchor="middle" fill="white" class="marker-circle">1</text>
            <text x="40" y="5" class="font-sans card-desc">引导式交互: 我只需说"测热迁移"，Agent就会自动<tspan x="40" dy="1.2em">查询并让我选择可用资源</tspan></text>
        </g>
        <g transform="translate(930, 285)">
            <circle cx="15" cy="0" r="12" fill="#f97316"/>
            <text x="15" y="5" text-anchor="middle" fill="white" class="marker-circle">2</text>
            <text x="40" y="5" class="font-sans card-desc">智能容错: 就算一个集群失败了，Agent也会自动<tspan x="40" dy="1.2em">尝试下一个，无需我手动干预</tspan></text>
        </g>
        <g transform="translate(930, 330)">
            <circle cx="15" cy="0" r="12" fill="#f97316"/>
            <text x="15" y="5" text-anchor="middle" fill="white" class="marker-circle">3</text>
            <text x="40" y="5" class="font-sans card-desc">加速闭环: 可以在快速完成"开发-验证"的<tspan x="40" dy="1.2em">全过程，效率大大提升</tspan></text>
        </g>
    </g>

    <!-- Main Architecture -->
    <text x="850" y="410" class="font-sans section-title">核心工作流与技术底座</text>
    <g id="main-blocks" transform="translate(0, 50)">
        <!-- User & UI -->
        <g id="user-interface">
            <rect x="50" y="400" width="300" height="880" rx="16" fill="url(#headerGradient)" filter="url(#dropShadow)"/>
            <text x="200" y="440" class="font-sans card-title" text-anchor="middle">🔗 用户交互界面</text>
            <text x="200" y="460" class="font-sans card-subtitle" text-anchor="middle">CloudBot / 钉钉</text>
            <g transform="translate(70, 500)">
                <circle cx="12" cy="0" r="12" fill="#f97316"/>
                <text x="12" y="5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">★</text>
                <text x="35" y="5" class="highlight-text">开发效率提升</text>
            </g>
            <text x="70" y="525" class="font-sans card-desc">从繁琐填表到智能对话，<tspan x="70" dy="1.2em">测试准备时间大幅降低</tspan></text>
            <g transform="translate(70, 580)">
                <circle cx="12" cy="0" r="12" fill="#f97316"/>
                <text x="12" y="5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">★</text>
                <text x="35" y="5" class="highlight-text">统一体验</text>
            </g>
            <text x="70" y="605" class="font-sans card-desc">一个界面完成全流程，<tspan x="70" dy="1.2em">减少上下文切换成本</tspan></text>
            <text x="200" y="680" class="font-sans card-desc" text-anchor="middle" font-style="italic">"你好，我想测试一下热迁移。"</text>
            <rect x="75" y="1180" width="250" height="60" rx="8" fill="rgba(255,255,255,0.3)"/>
            <text x="200" y="1215" class="font-sans card-desc" text-anchor="middle" font-style="italic">"为您找到2个用例，请选择..."</text>
        </g>

        <!-- Agent -->
        <g id="agent-core">
            <rect x="400" y="400" width="600" height="880" rx="16" fill="url(#agentGradient)" filter="url(#dropShadow)"/>
            <text x="700" y="440" class="font-sans card-title" text-anchor="middle">🤖 测试智能体</text>
            <g transform="translate(420, 470)">
                <circle cx="12" cy="0" r="12" fill="#f97316"/>
                <text x="12" y="5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">★</text>
                <text x="35" y="5" class="highlight-text">智能容错能力</text>
            </g>
            <text x="420" y="495" class="font-sans card-desc">自动重试与备选方案</text>
            
            <!-- ReAct Loop -->
            <g id="react-loop" transform="translate(50, 25)">
                <path d="M 650 500 A 100 80 0 1 1 650 660 A 100 80 0 1 1 650 500" fill="none" stroke="rgba(255,255,255,0.5)" stroke-width="2" stroke-dasharray="4 4"/>
                <g transform="translate(450, 520)">
                    <rect x="0" y="0" width="160" height="70" rx="10" fill="rgba(255,255,255,0.2)" fill-opacity="0.7"/>
                    <text x="80" y="25" class="font-sans react-step">Thought</text>
                    <text x="80" y="45" class="font-sans react-desc">用户想测热迁移，</text>
                    <text x="80" y="58" class="font-sans react-desc">我需先找相关用例。</text>
                </g>
                <g transform="translate(690, 520)">
                    <rect x="0" y="0" width="160" height="70" rx="10" fill="rgba(255,255,255,0.2)" fill-opacity="0.7"/>
                    <text x="80" y="25" class="font-sans react-step">Action</text>
                    <text x="80" y="45" class="font-sans react-desc">调用工具查询:</text>
                    <text x="80" y="58" class="font-sans code">getFvtCase()</text>
                </g>
                <g transform="translate(570, 620)">
                    <rect x="0" y="0" width="160" height="70" rx="10" fill="rgba(255,255,255,0.2)" fill-opacity="0.7"/>
                    <text x="80" y="25" class="font-sans react-step">Observation</text>
                    <text x="80" y="45" class="font-sans react-desc">返回了2个可用Case</text>
                </g>
            </g>

            <!-- Technical Foundation -->
            <g id="foundational-tech" transform="translate(420, 760)">
                <text x="280" y="15" class="font-sans card-title" text-anchor="middle">🏗️ 技术底座</text>

                <!-- Context Engineering -->
                <g transform="translate(0, 45)">
                    <rect x="-10" y="-10" width="580" height="100" rx="8" fill="rgba(0,0,0,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
                    <rect x="0" y="0" width="560" height="80" rx="6" fill="rgba(0,0,0,0.15)"/>
                    <text x="280" y="25" text-anchor="middle" class="font-sans tech-title">上下文工程（Context Engineering)</text>
                    <text x="15" y="50" class="font-sans tech-desc">🎯 价值: 将模糊指令精确化，补全执行所需的所有参数</text>
                    <text x="15" y="70" class="font-sans tech-desc">💡 实现: 基于LangGraph状态管理 + FileSystem，动态构建完整执行上下文</text>
                </g>

                <!-- Iterative Planning -->
                <g transform="translate(0, 160)">
                    <rect x="-10" y="-10" width="580" height="100" rx="8" fill="rgba(0,0,0,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
                    <rect x="0" y="0" width="560" height="80" rx="6" fill="rgba(0,0,0,0.2)"/>
                    <text x="280" y="25" text-anchor="middle" class="font-sans tech-title">迭代式规划(Iterative Planning)</text>
                    <text x="15" y="50" class="font-sans tech-desc">🎯 价值: 故障后智能重试，自动选择备用方案，而不是直接失败</text>
                    <text x="15" y="70" class="font-sans tech-desc">💡 实现: ReAct模式 + 多轮对话，支持动态调整执行策略</text>
                </g>

                <!-- Multi-turn Conversation -->
                <g transform="translate(0, 275)">
                    <rect x="-10" y="-10" width="580" height="100" rx="8" fill="rgba(0,0,0,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
                    <rect x="0" y="0" width="560" height="80" rx="6" fill="rgba(0,0,0,0.15)"/>
                    <text x="280" y="25" text-anchor="middle" class="font-sans tech-title">多轮对话能力</text>
                    <text x="15" y="50" class="font-sans tech-desc">🎯 价值: 上下文感知与澄清，主动追问以避免错误假设</text>
                    <text x="15" y="70" class="font-sans tech-desc">💡 实现: 会话状态持久化 + 智能意图识别与参数补全</text>
                </g>

                <!-- Model Layer -->
                <g transform="translate(0, 390)">
                    <rect x="-10" y="-10" width="580" height="100" rx="8" fill="rgba(0,0,0,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
                    <rect x="0" y="0" width="560" height="80" rx="6" fill="rgba(0,0,0,0.2)"/>
                    <text x="280" y="25" text-anchor="middle" class="font-sans tech-title">模型层能力</text>
                    <text x="15" y="50" class="font-sans tech-desc">🎯 价值: (大模型: 通义千问) 提供强大的语言理解、推理和规划能力</text>
                    <text x="15" y="70" class="font-sans tech-desc">💡 实现: 是Agent大脑的智能核心</text>
                </g>
            </g>
        </g>

        <!-- Tools -->
        <g id="tools">
            <rect x="1050" y="400" width="275" height="880" rx="16" fill="url(#toolsGradient)" filter="url(#dropShadow)"/>
            <text x="1187.5" y="440" class="font-sans card-title" text-anchor="middle">🧩 MCP 工具集</text>
            <text x="1187.5" y="460" class="font-sans card-subtitle" text-anchor="middle">转化业务服务为大模型可用工具</text>
            <g transform="translate(1070, 500)">
                <circle cx="12" cy="0" r="12" fill="#f97316"/>
                <text x="12" y="5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">★</text>
                <text x="35" y="5" class="highlight-text">可扩展架构</text>
            </g>
            <text x="1070" y="525" class="font-sans card-desc">基于MCP标准，轻松集成<tspan x="1070" dy="1.2em">新的测试服务与工具</tspan></text>
            <g transform="translate(1070, 580)">
                <circle cx="12" cy="0" r="12" fill="#f97316"/>
                <text x="12" y="5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">★</text>
                <text x="35" y="5" class="highlight-text">企业级可靠性</text>
            </g>
            <text x="1070" y="605" class="font-sans card-desc">完整的监控、日志与<tspan x="1070" dy="1.2em">错误处理机制</tspan></text>

            <g transform="translate(1070, 660)">
                <rect x="0" y="0" width="235" height="100" rx="8" fill="rgba(255,255,255,0.2)"/>
                <text x="10" y="25" class="font-sans tech-title">技术特性</text>
                <text x="10" y="50" class="font-sans tech-desc">🔒 安全认证与权限控制</text>
                <text x="10" y="70" class="font-sans tech-desc">⚡ 异步调用与错误处理</text>
                <text x="10" y="90" class="font-sans tech-desc">📊 调用链路追踪与监控</text>
            </g>
        </g>

        <!-- FVT Service -->
        <g id="fvt-service">
            <rect x="1375" y="400" width="275" height="880" rx="16" fill="url(#techGradient)" filter="url(#dropShadow)"/>
            <text x="1512.5" y="440" class="font-sans card-title" text-anchor="middle">⚙️ 后端服务</text>
            <text x="1512.5" y="460" class="font-sans card-subtitle" text-anchor="middle">测试服务</text>
            <g transform="translate(1395, 500)">
                <circle cx="12" cy="0" r="12" fill="#f97316"/>
                <text x="12" y="5" font-size="12" fill="white" text-anchor="middle" font-weight="bold">★</text>
                <text x="35" y="5" class="highlight-text">企业级测试服务能力</text>
            </g>

            <!-- API Details -->
            <g transform="translate(1395, 540)">
                <rect x="0" y="0" width="235" height="200" rx="8" fill="rgba(255,255,255,0.2)"/>
                <text x="10" y="25" class="font-sans tech-title">核心API接口</text>
                <text x="10" y="50" class="font-sans code">getFvtResource()</text>
                <text x="10" y="70" class="font-sans tech-desc">获取可用资源<tspan x="10" dy="1.2em">(集群等)</tspan></text>
                <text x="10" y="100" class="font-sans code">getFvtSetCaseInfo()</text>
                <text x="10" y="120" class="font-sans tech-desc">查询测试用例信息</text>
                <text x="10" y="150" class="font-sans code">createFvtJob()</text>
                <text x="10" y="170" class="font-sans tech-desc">创建FVT测试任务</text>
            </g>
        </g>
    </g>

    <!-- Enhanced Flow Arrows -->
    <g class="font-sans">
        <path d="M 350 680 L 400 680" fill="none" stroke="#475569" stroke-width="3" marker-end="url(#arrowhead)"/>
        <text x="375" y="670" class="flow-label">用户输入</text>

        <path d="M 1000 580 L 1050 580" fill="none" stroke="#475569" stroke-width="3" marker-end="url(#arrowhead)"/>
        <text x="1025" y="570" class="flow-label">Action</text>

        <path d="M 1050 650 L 1000 650" fill="none" stroke="#475569" stroke-width="3" marker-end="url(#arrowhead)"/>
        <text x="1025" y="665" class="flow-label">Observation</text>

        <path d="M 1325 580 L 1375 580" fill="none" stroke="#475569" stroke-width="3" marker-end="url(#arrowhead)"/>
        <text x="1350" y="570" class="flow-label">API调用</text>

        <path d="M 400 1210 L 350 1210" fill="none" stroke="#475569" stroke-width="3" marker-end="url(#arrowhead)"/>
        <text x="375" y="1200" class="flow-label">结果反馈</text>
    </g>
</svg>
