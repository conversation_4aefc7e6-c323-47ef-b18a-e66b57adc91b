"""
Agent configuration constants.

This module contains agent-related configurations, including
LLM type definitions and agent-to-LLM mappings.
"""

from typing import Literal

# Define available LLM types
LLMType = Literal["basic", "reasoning", "vision"]

# Define agent-LLM mapping
AGENT_LLM_MAP: dict[str, LLMType] = {
    "coordinator": "reasoning",
    "planner": "reasoning",
    "researcher": "reasoning",
    "coder": "basic",
    "reporter": "reasoning",
    "podcast_script_writer": "basic",
    "ppt_composer": "basic",
    "prose_writer": "basic",
}

__all__ = [
    'LLMType',
    'AGENT_LLM_MAP'
]
