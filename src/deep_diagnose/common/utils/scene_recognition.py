"""
场景识别工具模块

提供基于诊断数据的智能场景识别功能，用于判断机器/实例的当前状态场景。
"""

import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)


class SceneRecognizer:
    """
    场景识别器

    基于诊断数据识别不同的系统场景，如宕机、维护、正常等状态。
    """

    def __init__(self):
        """初始化场景识别器"""
        # 定义宕机相关的监控异常类型
        self.down_monitor_exceptions = ["nc_down_alert", "vm_crash_event", "user_accept_event_to_avoid_issue"]

        # 宕机相关的 Ops Code
        self.down_ops_codes = [
            "Redeploy",
            "Reboot",
            "SystemFailure.Redeploy",
            "InstanceFailure.Reboot",
            "SystemMaintenance.Stop",
            "SystemMaintenance.AutoOps",
            "SystemFailure.Stop",
            "SystemFailure.Reboot",
            "SystemMaintenance.StopAndRepair",
        ]

        # Ops Code 中文映射
        self.ops_code_map = {
            "Reboot": "主动运维-重启",
            "Redeploy": "主动运维-重新部署",
            "SystemMaintenance.LiveMigrate": "主动运维-热迁移",
            "SystemMaintenance.Stop": "主动运维-停机",
            "SystemMaintenance.IsolateErrorDisk": "主动运维-在线隔离坏盘",
            "SystemMaintenance.RebootAndIsolateErrorDisk": "主动运维-隔离坏盘并重启",
            "SystemMaintenance.ReInitErrorDisk": "主动运维-在线恢复隔离磁盘",
            "SystemMaintenance.RebootAndReInitErrorDisk": "主动运维-恢复隔离磁盘并重启",
            "SystemMaintenance.StopAndRepair": "主动运维-停机维修",
            "SystemMaintenance.RepairErrorDisk": "主动运维-磁盘在线维修",
            "SystemFailure.Reboot": "NC非预期异常恢复运维-重启",
            "SystemFailure.Redeploy": "NC非预期异常恢复运维-重新部署",
            "SystemFailure.Stop": "NC非预期异常恢复运维-停机",
            "InstanceFailure.Reboot": "VM非预期异常恢复运维-重启",
        }

        self.chinese_down_ops_codes = [self.ops_code_map[code] for code in self.down_ops_codes]

    def recognize(self, diagnostic_data: Dict[str, Any]) -> Dict[str, str]:
        """
        识别系统场景

        Args:
            diagnostic_data: 诊断数据字典，包含监控异常和运维事件等信息

        Returns:
            Dict[str, str]: 识别结果，包含以下字段：
                - scene: 场景类型 ("default", "down", "maintenance" 等)
                - time: 相关事件时间 (可选)
                - reason: 识别原因描述 (可选)
        """
        result = {"scene": "default"}

        if not diagnostic_data:
            logger.debug("No diagnostic data provided for scene recognition")
            return result

        # 尝试识别宕机场景
        down_result = self._recognize_down_scene(diagnostic_data)
        if down_result["scene"] != "default":
            return down_result

        # 未来可以扩展其他场景识别
        # maintenance_result = self._recognize_maintenance_scene(diagnostic_data)
        # performance_result = self._recognize_performance_scene(diagnostic_data)

        logger.info("Scene recognition: no specific scene detected, using default")
        return result

    def _recognize_down_scene(self, diagnostic_data: Dict[str, Any]) -> Dict[str, str]:
        """
        识别宕机场景

        Args:
            diagnostic_data: 诊断数据

        Returns:
            Dict[str, str]: 宕机场景识别结果
        """
        result = {"scene": "default"}

        # 安全获取监控异常数据
        monitor_exceptions = self._safe_get_dict(diagnostic_data, "listCategorizedMonitorExceptions")
        customer_events = self._safe_get_dict(diagnostic_data, "listReportedOperationalEvents")

        # 获取关键指标异常
        keymetric = self._safe_get_list(monitor_exceptions, "keymetric")

        # 获取客户事件
        unfinished_events = self._safe_get_list(customer_events, "unfinished")
        finished_events = self._safe_get_list(customer_events, "finished")

        # 如果没有任何异常或事件，返回默认场景
        if not keymetric and not unfinished_events and not finished_events:
            return result

        # 检查关键指标异常中的宕机信号
        down_result = self._check_keymetric_down(keymetric)
        if down_result["scene"] != "default":
            return down_result

        # 检查已完成事件中的宕机信号
        down_result = self._check_events_down(finished_events, "finished")
        if down_result["scene"] != "default":
            return down_result

        # 检查未完成事件中的宕机信号
        down_result = self._check_events_down(unfinished_events, "unfinished")
        if down_result["scene"] != "default":
            return down_result

        return result

    def _check_keymetric_down(self, keymetric: List[Dict[str, Any]]) -> Dict[str, str]:
        """检查关键指标异常中的宕机信号"""
        for event in keymetric:
            if not isinstance(event, dict):
                continue

            monitor_name = event.get("exceptionName", "")
            if monitor_name in self.down_monitor_exceptions:
                result = {"scene": "down", "time": event.get("lastExceptionTime", ""), "reason": f"Keymetric exception: {monitor_name}"}
                logger.info(f"Scene recognition: detected 'down' from keymetric - {monitor_name}")
                return result

        return {"scene": "default"}

    def _check_events_down(self, events: List[Dict[str, Any]], event_type: str) -> Dict[str, str]:
        """检查运维事件中的宕机信号"""
        for event in events:
            if not isinstance(event, dict):
                continue
            ops_code = event.get("opsCode", "")

            if ops_code in self.down_ops_codes or ops_code in self.chinese_down_ops_codes:
                result = {"scene": "down", "time": event.get("eventStart", ""), "reason": f"{event_type} event: {ops_code}"}
                logger.info(f"Scene recognition: detected 'down' from {event_type} events - {ops_code}")
                return result

        return {"scene": "default"}

    def _safe_get_dict(self, data: Dict[str, Any], key: str) -> Dict[str, Any]:
        """安全获取字典值"""
        value = data.get(key, {})
        return value if isinstance(value, dict) else {}

    def _safe_get_list(self, data: Dict[str, Any], key: str) -> List[Dict[str, Any]]:
        """安全获取列表值"""
        value = data.get(key, [])
        if isinstance(value, list):
            return [item for item in value if isinstance(item, dict)]
        return []


# 全局实例，方便直接调用
scene_recognizer = SceneRecognizer()


def recognize_scene(diagnostic_data: Dict[str, Any]) -> Dict[str, str]:
    """
    便捷函数：识别系统场景

    Args:
        diagnostic_data: 诊断数据字典

    Returns:
        Dict[str, str]: 场景识别结果
    """
    return scene_recognizer.recognize(diagnostic_data)


def recognize_down_scene(diagnostic_data: Dict[str, Any]) -> bool:
    """
    便捷函数：判断是否为宕机场景

    Args:
        diagnostic_data: 诊断数据字典

    Returns:
        bool: True表示识别为宕机场景，False表示非宕机场景
    """
    result = scene_recognizer.recognize(diagnostic_data)
    return result.get("scene") == "down"


def get_scene_info(diagnostic_data: Dict[str, Any]) -> tuple[str, str]:
    """
    便捷函数：获取场景信息

    Args:
        diagnostic_data: 诊断数据字典

    Returns:
        tuple[str, str]: (场景类型, 相关时间)
    """
    result = scene_recognizer.recognize(diagnostic_data)
    return result.get("scene", "default"), result.get("time", "")
