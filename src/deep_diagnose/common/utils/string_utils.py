from typing import List


def is_blank(s: str) -> bool:
    """
    Check if a string is blank (none, empty or only contains whitespace).

    Args:
        s (str): String to check

    Returns:
        bool: True if string is blank, False otherwise
    """
    return s is None or not bool(s.strip())


def contains_keywords(s: str, keywords: List[str]) -> bool:
    """
    Check if a given string contains any of the specified keywords, case-insensitive.

    Args:
        s (str): The string to check for keywords.
        keywords (List[str]): A list of keyword strings to search for.

    Returns:
        bool: True if any keyword is found in the string, False otherwise.
    """
    # Early return if keywords list is empty or the input string is blank
    if not keywords or is_blank(s):
        return False

    # Convert the input string to lowercase for case-insensitive comparison
    s = s.lower()

    # Iterate over each keyword and check if it exists in the string
    for keyword in keywords:
        if keyword and keyword.lower() in s:
            return True

    # Return False if none of the keywords are found
    return False


def contains_other_characters(s: str, allowed_chars: set) -> bool:
    """
    判断字符串是否包含除特定字符外的其他字符。

    Args:
        s (str): 要检查的字符串。
        allowed_chars (str): 允许的特定字符组成的字符串。

    Returns:
        bool: 如果字符串包含除特定字符外的其他字符，返回 True；否则返回 False。
    """
    # 如果字符串为空或 allowed_chars 为空，直接返回 False
    if not s or not allowed_chars:
        return False

    # 将允许的字符转换为集合，提高查找效率
    allowed_set = set(allowed_chars)

    # 遍历字符串中的每个字符
    for char in s:
        if char not in allowed_set:
            return True  # 发现不允许的字符，返回 True

    return False  # 所有字符都在允许的集合中，返回 False


