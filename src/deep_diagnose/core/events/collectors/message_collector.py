"""
消息收集器

负责收集和处理原始消息数据，存储到MessageRepository
职责：
1. 提取消息基础信息（agent_name, run_id）
2. 处理AI消息内容和工具调用
3. 处理工具执行结果
4. 存储到MessageRepository
"""

import json
import logging
import re
from dataclasses import dataclass
from typing import Union, Dict, Any, List, Optional, Set

from langchain_core.messages import AIMessageChunk, ToolMessage

from ..models import ToolExecution, MessageChunk
from ..models.agent_message import merge_incremental_text
from ..storage import MessageRepository

logger = logging.getLogger(__name__)


class MessageCollector:
    @dataclass
    class Change:
        agent_name: Optional[str]
        change_type: str  # 'content' | 'tool' | 'finish' | 'unknown'
    """
    消息收集器 - 处理原始消息并存储到MessageRepository

    设计原则：
    - 单一职责：只负责消息收集和存储
    - 无状态：不保存任何状态信息
    - 纯函数：输入确定，输出确定
    """

    def collect_message(self, message_repository: MessageRepository, message: Union[AIMessageChunk, ToolMessage],
                        message_metadata: dict) -> Optional["MessageCollector.Change"]:
        """
        收集消息并存储到MessageRepository

        处理流程：
        1. 识别消息类型
        2. 提取关键信息
        3. 存储到MessageRepository

        Args:
            message_repository: MessageRepository实例
            message: 原始消息（AIMessageChunk或ToolMessage）
            message_metadata: 消息元数据

        Returns:
            Optional[MessageCollector.Change]: 更改信息（包含变更的 agent 和类型），失败返回 None
        """
        try:
            # 过滤带有 "silent_llm_call" 标签的消息
            tags = message_metadata.get('tags', [])
            if tags and "silent_llm_call" in tags:
                return None

            if isinstance(message, AIMessageChunk):
                return self._handle_ai_message(message_repository, message, message_metadata)
            elif isinstance(message, ToolMessage):
                return self._handle_tool_message(message_repository, message)
            else:
                logger.debug(f"Unsupported message type: {type(message)}")
                return None

        except Exception as e:
            logger.error(f"Error collecting message: {e}", exc_info=True)
            return False

    def _handle_ai_message(self, message_repository: MessageRepository, message: AIMessageChunk, message_metadata: dict) -> Optional["MessageCollector.Change"]:
        """
        处理AI消息

        步骤：
        1. 提取基础信息（agent_name, run_id）
        2. 提取消息内容
        3. 提取工具调用信息
        4. 存储原始数据
        5. 更新完成状态
        """
        # 1. 提取基础信息
        agent_name, run_id, langgraph_id, tags,metadata = self._extract_basic_info(message, message_metadata)
        if not agent_name or not run_id:
            return False

        # 2. 获取或创建消息记录
        agent_message = message_repository.get_or_create_agent_message(agent_name, run_id, langgraph_id, tags,metadata)

        change_type: Optional[str] = None
        # 3. 处理消息内容
        if self._process_message_content(agent_message, message):
            change_type = 'content'

        # 4. 处理工具调用
        if self._process_tool_executions(agent_message, message, agent_name):
            change_type = 'tool' if change_type is None else change_type

        # 5. 存储原始数据
        self._store_message_chunk(agent_message, message)
        # 调试写文件逻辑已移除，避免在 backend/src/tmp_planner_debug 目录输出临时文件

        # 6. 更新完成状态
        if self._update_finish_status(agent_message, message):
            change_type = 'finish'

        return MessageCollector.Change(agent_name=agent_name, change_type=change_type or 'unknown')

    def _handle_tool_message(self, message_repository: MessageRepository, message: ToolMessage) -> Optional["MessageCollector.Change"]:
        """
        处理工具消息

        步骤：
        1. 提取工具调用ID
        2. 查找对应的消息记录
        3. 解析工具执行结果
        4. 更新工具执行状态
        """
        # 1. 提取工具调用ID
        tool_call_id = getattr(message, 'tool_call_id', '')
        if not tool_call_id:
            return None

        # 2. 查找对应的消息记录
        agent_message = message_repository.find_message_by_tool_execution(tool_call_id)
        if not agent_message:
            logger.warning(f"No message found for tool_call_id: {tool_call_id}")
            return False

        # 3. 解析工具执行结果
        result_data = self._parse_tool_result(message)
        if result_data is None:
            return None

        # 4. 更新工具执行状态
        updated = self._update_tool_execution_result(agent_message, tool_call_id, result_data)
        if not updated:
            return None
        return MessageCollector.Change(agent_name=agent_message.agent, change_type='tool')

    # ==================== 辅助方法 ====================

    def _extract_basic_info(self, message: AIMessageChunk, message_metadata: dict) -> tuple:
        """提取基础信息"""

        # 优先从消息对象获取 id
        run_id = getattr(message, 'id', '') or ""
        langgraph_id = message_metadata.get('langgraph_checkpoint_ns', '') or ''
        # 提取 agent 名称
        if langgraph_id:
            agent_name = langgraph_id.split(":")[0]
        else:
            agent_name = message_metadata.get('langgraph_node', '') or ''
        # 进一步回退：从 tags 猜测 agent
        tags = message_metadata.get('tags', []) or []
        #metadata = message_metadata.get('configurable', {}).get("step_num", -1)

        # 通用推断：从 tags 中寻找形如 "<name>:..." 的格式，取冒号前作为 agent_name
        if not agent_name and isinstance(tags, list):
            for t in tags:
                s = str(t).strip()
                if ":" in s:
                    head = s.split(":", 1)[0].strip()
                    if head:
                        agent_name = head.lower()
                        break
        # 进一步回退：从 message.name 获取
        if not agent_name:
            agent_name = getattr(message, 'name', '') or ''
        # run_id 回退：从元数据中尝试，最后生成一个稳定的基于 langgraph_id 的回退
        if not run_id:
            run_id = (
                message_metadata.get('id')
                or message_metadata.get('run_id')
                or message_metadata.get('thread_id')
                or langgraph_id
                or ''
            )
        # 如果仍为空，生成一个临时 id（极端情况下）
        if not run_id:
            from uuid import uuid4
            run_id = f"gen_{uuid4()}"
        logger.debug(f"agent_name: {agent_name}, run_id: {run_id}, langgraph_id: {langgraph_id}")
        return agent_name, run_id, langgraph_id, tags, 0

    def _process_message_content(self, agent_message, message: AIMessageChunk) -> bool:
        """处理消息内容"""
        content = getattr(message, 'content', '')
        reasoning_content = getattr(message, 'reasoning_content', '')

        # 某些提供方会在流式中返回 None；统一归一为空字符串避免丢失逻辑
        if content is None:
            content = ""
        if reasoning_content is None:
            reasoning_content = ""

        # 直接追加，不对传入做截断或过滤；合并逻辑在 AgentMessage.update_content 里负责去重与重叠处理
        if content != "" or reasoning_content != "":
            agent_message.update_content(content, reasoning_content)
            return True
        return False

    def _process_tool_executions(self, agent_message, message: AIMessageChunk, agent_name: str) -> bool:
        """处理工具执行 - 收集所有工具调用，不做过滤"""
        tool_calls = self._extract_tool_calls(agent_message, message)
        updated = False

        for tool_call_data in tool_calls:
            call_id = tool_call_data.get("id", "")
            all_call_id = [tool_execution.call_id for tool_execution in agent_message.tool_executions]
            if call_id in all_call_id:
                index = all_call_id.index(call_id)
                tool_execution = agent_message.tool_executions[index]
                # 增量参数合并：支持快照与增量，避免重复与嵌套
                current_args = tool_execution.call_args or ""
                new_args_raw = tool_call_data.get("args")
                new_args = self._normalize_args(new_args_raw)
                tool_execution.call_args = self._merge_incremental_args(current_args, new_args)
            else:
                # 新建：归一化初始参数
                call_args = self._normalize_args(tool_call_data.get("args"))
                tool_execution = ToolExecution(
                    call_id=tool_call_data.get("id", ""),
                    call_name=tool_call_data.get("name", ""),
                    call_args=call_args
                )
                agent_message.add_tool_execution(tool_execution)
            updated = True

        return updated

    def _store_message_chunk(self, agent_message, message: AIMessageChunk):
        """存储原始数据片段"""
        message_chunk = MessageChunk(
            content=getattr(message, 'content', '') or "",
            reasoning_content=getattr(message, 'reasoning_content', '') or "",
            metadata={
                "finish_reason": self._extract_finish_reason(message),
                "response_metadata": getattr(message, 'response_metadata', {})
            }
        )
        agent_message.add_chunk(message_chunk)

    def _update_finish_status(self, agent_message, message: AIMessageChunk) -> bool:
        """更新完成状态"""
        finish_reason = self._extract_finish_reason(message)
        if finish_reason:
            agent_message.set_finished(finish_reason)
            return True
        return False

    def _parse_tool_result(self, message: ToolMessage) -> dict:
        """解析工具执行结果"""
        content = getattr(message, 'content', '')
        if not content:
            return None

        try:
            return json.loads(content)
        except (json.JSONDecodeError, TypeError):
            return {"raw_content": content}

    def _update_tool_execution_result(self, agent_message, tool_call_id: str, result_data: dict) -> bool:
        """更新工具执行结果"""
        tool_execution = agent_message.find_tool_execution(tool_call_id)
        if tool_execution:
            tool_execution.mark_completed(result_data)
            return True
        return False

    # ==================== 数据提取方法 ====================

    def _extract_tool_calls(self, agent_message, message: AIMessageChunk) -> List[Dict[str, Any]]:
        """
        提取工具调用信息
        """
        tool_calls = []
        if hasattr(message, 'tool_call_chunks') and message.tool_call_chunks:
            tool_call_chunks = message.tool_call_chunks
            for tool_call_chunk in tool_call_chunks:
                if isinstance(tool_call_chunk, dict):
                    tool_call_id = tool_call_chunk.get("id", "")
                    if not tool_call_id and len(agent_message.tool_executions) > 0:
                        tool_call_id = agent_message.tool_executions[-1].call_id
                    tool_calls.append({
                        "id": tool_call_id,
                        "name": tool_call_chunk.get("name", ""),
                        "args": tool_call_chunk.get("args", ""),
                        "type": tool_call_chunk.get("type", "tool_call_chunk")
                    })
        return tool_calls

    def _extract_finish_reason(self, message: AIMessageChunk) -> str:
        """提取完成原因"""
        if hasattr(message, 'response_metadata'):
            return message.response_metadata.get("finish_reason")
        return None

    # ==================== 工具参数处理 ====================

    def _parse_tool_arguments(self, arguments: str) -> Dict[str, Any]:
        """
        解析工具参数

        支持：
        1. 标准JSON格式
        2. 不完整JSON的修复
        3. 原始字符串保存
        """
        if not arguments.strip():
            return {}

        # 1. 尝试直接解析
        try:
            return json.loads(arguments)
        except (json.JSONDecodeError, TypeError):
            pass

        # 2. 尝试修复不完整的JSON
        try:
            fixed_args = self._fix_incomplete_json(arguments)
            return json.loads(fixed_args)
        except (json.JSONDecodeError, TypeError):
            pass

        # 3. 保存原始字符串
        return {"raw_arguments": arguments}

    def _fix_incomplete_json(self, arguments: str) -> str:
        """修复不完整的JSON（尽量不破坏已有结构）"""
        fixed_args = (arguments or "").strip()

        # 快速路径：看起来像JSON，直接返回
        if (fixed_args.startswith('{') and fixed_args.endswith('}')) or (fixed_args.startswith('[') and fixed_args.endswith(']')):
            return fixed_args

        # 最小化修复：
        # - 处理常见的尾部丢失引号/逗号/右括号
        # - 去掉重复的包裹（例如把已有的JSON又当作字符串包一次）
        # 基本策略：
        # 1) 去掉首尾多余的引号
        if (fixed_args.startswith('"') and fixed_args.endswith('"')) or (fixed_args.startswith("'") and fixed_args.endswith("'")):
            fixed_args = fixed_args[1:-1]

        # 2) 如果内部已经是一个完整的对象/数组，返回
        s = fixed_args.strip()
        if (s.startswith('{') and s.endswith('}')) or (s.startswith('[') and s.endswith(']')):
            return s

        # 3) 兜底：包一层对象
        if not s.startswith('{'):
            s = '{' + s
        if not s.endswith('}'):
            s = s + '}'
        return s

    # =============== 参数合并辅助（增量->全量）===============
    def _normalize_args(self, args_value: Any) -> str:
        """将不同形式的参数统一为字符串（JSON文本）"""
        if args_value is None:
            return ""
        if isinstance(args_value, (dict, list)):
            try:
                return json.dumps(args_value, ensure_ascii=False)
            except Exception:
                return str(args_value)
        return str(args_value)

    def _merge_incremental_args(self, existing: str, new_value: str, window: int = 120) -> str:
        """合并工具参数：
        - 处理增量（delta）与快照（snapshot）两种流式模式
        - 避免重复、嵌套与错位
        - 策略同 merge_incremental_text，但更大的窗口，提高重叠识别
        """
        if not new_value:
            return existing or ""
        if not existing:
            return new_value

        # 快照：新值包含旧值前缀 -> 使用新值
        if new_value.startswith(existing):
            return new_value
        # 重复尾部：旧值以新值结尾 -> 保留旧值
        if existing.endswith(new_value):
            return existing

        # 查找重叠（后缀/前缀）
        max_overlap = min(len(existing), len(new_value), window)
        for k in range(max_overlap, 0, -1):
            if existing[-k:] == new_value[:k]:
                return existing + new_value[k:]

        # 兜底：直接拼接（宁重复，不丢失）
        return existing + new_value
