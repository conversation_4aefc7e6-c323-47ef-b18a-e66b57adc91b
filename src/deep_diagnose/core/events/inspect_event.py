import json
import logging
from dataclasses import dataclass, field
from typing import Any, Optional

from deep_diagnose.common.utils.machine_utils import MachineIdType
from deep_diagnose.core.events.base_event import BaseAgentOutputEvent, EnumEncoder
from deep_diagnose.domain.chat.models import MessageStatus

logger = logging.getLogger(__name__)


@dataclass
@BaseAgentOutputEvent.register_event_type("inspect")
class InspectEvent(BaseAgentOutputEvent):
    """Inspect Agent 输出事件"""

    # 基本信息
    machine_id: str = ""
    start_time: str = ""
    end_time: str = ""
    machine_id_type: MachineIdType = MachineIdType.UNKNOWN

    # 状态信息
    finished: bool = False

    # 结果信息
    recommendations: list[dict[str, Any]] = field(default_factory=list)
    overview: str = ""

    # 可选的附加信息
    info_data: dict[str, Any] = field(default_factory=dict)

    def parse(self, raw_event: str) -> bool:
        """
        解析原始事件并更新自身状态

        Args:
            raw_event: 原始 SSE 事件字符串

        Returns:
            bool: 是否成功解析并更新了状态
        """
        try:
            # 假设 raw_event 是 JSON 格式的事件数据
            data = json.loads(raw_event)

            # 更新状态信息
            updated = False

            if "overview" in data and data["overview"]:
                self.overview = data["overview"]
                updated = True

            if "recommendations" in data and data["recommendations"]:
                self.recommendations = data["recommendations"]
                updated = True

            if "finished" in data:
                self.finished = data["finished"]
                updated = True

            if "machine_id" in data:
                self.machine_id = data["machine_id"]
                updated = True

            # 更新其他字段
            for field_name in ["start_time", "end_time", "machine_id_type", "info_data"]:
                if field_name in data:
                    setattr(self, field_name, data[field_name])
                    updated = True

            return updated

        except Exception as e:
            logger.error(f"Failed to parse overview event: {e}")
            return False

    def _is_final_event(self, event_data: str) -> bool:
        """
        判断是否为最终事件

        Args:
            event_data: SSE 格式的事件数据字符串

        Returns:
            bool: 是否为最终事件
        """
        try:
            data = json.loads(event_data)
            # Inspect 事件在 finished=True 或有完整的 overview 内容时认为是最终事件
            return data.get("finished", False) or (data.get("overview", "") and data.get("recommendations", []))
        except json.JSONDecodeError:
            return True

    def to_sse_format(self) -> str:
        """将事件对象序列化为 SSE 格式的字符串。"""
        # 直接构建 payload 字典，避免重复赋值
        payload = {
            "machine_id": self.machine_id,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "message": self.overview,
            "recommendations": self.recommendations,
            "event_type": self.event_type,
        }
        return json.dumps(payload, ensure_ascii=False, cls=EnumEncoder)

    @classmethod
    def from_sse_format(cls, event_data: str) -> Optional["InspectEvent"]:
        """
        从 SSE 格式反序列化 Inspect 事件

        Args:
            event_data: SSE 格式的事件数据字符串

        Returns:
            InspectEvent: 反序列化后的事件对象，如果解析失败则返回 None
        """
        try:
            data = json.loads(event_data)

            # 创建事件对象并填充数据
            event = cls()

            # 填充基本字段
            event.machine_id = data.get("machine_id", "")
            event.start_time = data.get("start_time", "")
            event.end_time = data.get("end_time", "")
            event.overview = data.get("overview", "")
            event.finished = data.get("finished", False)
            event.recommendations = data.get("recommendations", [])
            event.info_data = data.get("info_data", {})

            # 处理 machine_id_type
            machine_id_type_str = data.get("machine_id_type")
            if machine_id_type_str:
                try:
                    event.machine_id_type = MachineIdType(machine_id_type_str)
                except ValueError:
                    event.machine_id_type = MachineIdType.UNKNOWN

            return event

        except Exception as e:
            logger.error(f"Failed to deserialize overview event: {e}")
            return None

    def update_overview_content(self, content: str) -> None:
        """
        更新 overview 内容（累积式）

        Args:
            content: 要追加的内容
        """
        if content:
            self.overview += content

    def add_recommendation(self, recommendation: dict[str, Any]) -> None:
        """
        添加推荐项

        Args:
            recommendation: 推荐项数据
        """
        if recommendation and recommendation not in self.recommendations:
            self.recommendations.append(recommendation)

    def set_machine_info(self, machine_id: str, start_time: str, end_time: str, machine_id_type: MachineIdType = MachineIdType.UNKNOWN) -> None:
        """
        设置机器信息

        Args:
            machine_id: 机器ID
            start_time: 开始时间
            end_time: 结束时间
            machine_id_type: 机器ID类型
        """
        self.machine_id = machine_id
        self.start_time = start_time
        self.end_time = end_time
        self.machine_id_type = machine_id_type

    def mark_finished(self) -> None:
        """标记为完成状态"""
        self.finished = True

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式（用于调试和日志）"""
        return {
            "event_type": self.event_type,
            "machine_id": self.machine_id,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "machine_id_type": self.machine_id_type.value if self.machine_id_type else None,
            "finished": self.finished,
            "overview_length": len(self.overview),
            "recommendations_count": len(self.recommendations),
            "has_info_data": bool(self.info_data),
        }

    def to_persist_format(self) -> dict[str, Any]:
        """转换为持久化格式"""
        return {
            "message": self.overview,
            "ext": {
                "recommendations": self.recommendations,
            },
            "status": MessageStatus.SUCCESS.value if self.finished else MessageStatus.EXECUTING.value,
        }
