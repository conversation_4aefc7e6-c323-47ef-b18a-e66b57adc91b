"""FVT功能验证测试模块

完整的FVT（Functional Verification Testing）框架实现。
提供智能体、工作流、事件处理等核心组件，支持自动化的功能验证测试。

主要组件:
- FVTAgent: 主智能体，负责协调整个FVT流程
- FVTSubAgent: 子智能体，执行具体的测试任务
- FVTState: 工作流状态模型
- FVTAgentEvent: 事件处理模型
- 工作流构建器: 支持带内存和无状态两种模式

使用示例:
    >>> from deep_diagnose.core.fvt import FVTAgent
    >>> agent = FVTAgent()
    >>> async for event in agent.astream("执行FVT测试"):
    ...     print(event.result)
"""

from .agents import FVTSubAgent, fvt_agent_node
from .events import FVTAgentEvent
from .fvt_agent import FVTAgent
from .workflow import FVTState, build_fvt_graph, build_fvt_graph_with_memory, get_default_graph

__all__ = [
    # 主要智能体类
    "FVTAgent",
    "FVTSubAgent",
    
    # 工作流相关
    "FVTState",
    "build_fvt_graph",
    "build_fvt_graph_with_memory",
    "get_default_graph",
    "fvt_agent_node",
    
    # 事件处理
    "FVTAgentEvent",
]

# 版本信息
__version__ = "1.0.0"