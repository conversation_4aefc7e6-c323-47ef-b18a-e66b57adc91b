"""FVT子智能体实现

基于SubAgent基类的FVT功能验证测试智能体实现。
负责执行具体的FVT测试任务，集成MCP工具和ReactAgent执行模式。
"""

import logging
from typing import Any, List, Literal, Optional

from langchain_core.messages import SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent
from langgraph.types import Command

from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
from deep_diagnose.common.config.core.configuration import Configuration
from deep_diagnose.core.fvt.workflow.types import FVTState
from deep_diagnose.core.reasoning.agents.base import SubAgent
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager
from deep_diagnose.tools.python_repl import python_repl_tool

logger = logging.getLogger(__name__)


class FVTSubAgent(SubAgent):
    """FVT功能验证测试子智能体
    
    基于SubAgent基类实现，专门负责功能验证测试任务的执行。
    集成MCP工具管理器，支持动态工具加载和ReactAgent执行模式。
    
    Attributes:
        config_obj: 系统配置对象
        mcp_tool_manager: MCP工具管理器实例
        _fvt_tool_names: FVT相关工具名称列表
    """
    
    # FVT相关工具名称常量
    _FVT_TOOL_NAMES = [
        "createFvtJob",
        "getFvtResource",
        "getFvtSetCaseInfo", 
        "getFvtJobDetails"
    ]
    
    def __init__(self, config_obj: Configuration) -> None:
        """初始化FVT子智能体
        
        Args:
            config_obj: 系统配置对象，用于获取LLM和工具配置
        """
        super().__init__("fvt", "fvt", role="功能验证测试专家", llm_type="reasoning")
        self.config_obj = config_obj
        self.mcp_tool_manager = MCPToolManager()
    
    async def get_fvt_tools(self) -> List[Any]:
        """获取FVT相关的MCP工具
        
        从MCP工具管理器中筛选出FVT相关的工具。
        
        Returns:
            List[Any]: FVT相关工具列表，失败时返回空列表
        """
        try:
            # 获取所有可用的MCP工具
            all_tools = await self.mcp_tool_manager.get_enabled_mcp_tools()
            
            # 按名称过滤FVT相关工具
            fvt_tools = [
                tool for tool in all_tools 
                if tool.name in self._FVT_TOOL_NAMES
            ]
            
            logger.info(
                f"成功加载 {len(fvt_tools)} 个FVT工具: "
                f"{[tool.name for tool in fvt_tools]}"
            )
            return fvt_tools
            
        except Exception as e:
            logger.error(f"获取FVT工具失败: {e}")
            return []
    
    async def get_fvt_tools_description(self) -> str:
        """获取FVT工具的详细描述
        
        从MCP工具管理器中获取FVT相关工具的详细描述信息。
        
        Returns:
            str: FVT工具的Markdown格式描述，失败时返回默认描述
        """
        try:
            # 使用FVT工具名称列表获取描述
            description = await self.mcp_tool_manager.get_enabled_mcp_tools_description(
                tool_names=self._FVT_TOOL_NAMES
            )
            logger.info(f"成功获取FVT工具描述，长度: {len(description)} 字符")
            return description
            
        except Exception as e:
            logger.error(f"获取FVT工具描述失败: {e}")
            return "## Available Tools\nFVT工具暂时不可用，请检查系统配置。"
    
    def get_default_tools(self) -> List[Any]:
        """获取默认工具集
        
        返回基础工具列表，MCP工具将在执行时动态加载。
        
        Returns:
            List[Any]: 包含python_repl_tool的默认工具列表
        """
        return [python_repl_tool]
    
    async def _do_execute(self, state: FVTState, config: Optional[RunnableConfig] = None) -> Command:
        """执行FVT测试逻辑
        
        使用ReactAgent执行具体的功能验证测试任务。
        
        Args:
            state: FVT状态对象，包含消息和观察信息
            config: 运行时配置，可选
            
        Returns:
            Command: 工作流控制命令，指向下一个节点
        """
        try:
            # 准备工具和模型
            tools = await self._prepare_tools()
            llm = self._prepare_llm()
            
            # 创建并执行ReactAgent
            agent = await self._create_react_agent(llm, tools)
            result = await agent.ainvoke(
                {"messages": state.get("messages", [])}, 
                config=config
            )
            
            # 简单更新状态
            if "messages" in result:
                state["messages"] = result["messages"]
            
            logger.info("FVT功能验证测试执行完成")
            return Command(goto="__end__")
            
        except Exception as e:
            logger.error(f"FVT功能验证测试执行失败: {e}")
            return Command(goto="__end__")
            
    async def _prepare_tools(self) -> List[Any]:
        """准备执行所需的工具集"""
        fvt_tools = await self.get_fvt_tools()
        return fvt_tools
        
    def _prepare_llm(self) -> Any:
        """准备并配置LLM"""
        llm = get_llm_by_type(AGENT_LLM_MAP.get("fvt", "reasoning"))
        
        # 启用并发工具调用
        if hasattr(llm, 'parallel_tool_calls'):
            llm.parallel_tool_calls = True
        elif hasattr(llm, 'bind'):
            llm = llm.bind(parallel_tool_calls=True)
            
        return llm
        
    async def _create_react_agent(self, llm: Any, tools: List[Any]) -> Any:
        """创建配置好的ReactAgent"""
        return create_react_agent(
            model=llm,
            tools=tools,
            prompt=self._generate_fvt_prompt
        )
        
    async def _generate_fvt_prompt(self, agent_state: dict) -> List[SystemMessage]:
        """生成FVT专用提示词
        
        Args:
            agent_state: Agent状态字典
            
        Returns:
            List[SystemMessage]: 系统消息列表
        """
        try:
            # 获取FVT工具描述
            mcp_tool_description = await self.get_fvt_tools_description()
            
            # 构建FVT状态用于模板应用
            fvt_state = FVTState()
            fvt_state["messages"] = agent_state.get("messages", [])
            fvt_state["mcp_tool_description"] = mcp_tool_description
            
            return apply_prompt_template("agent_fvt", fvt_state)
            
        except Exception as e:
            logger.warning(f"使用FVT模板失败，使用默认提示: {e}")
            return [SystemMessage(
                content="你是一个功能验证测试(FVT)专家。请严格按照SOP流程创建和管理FVT测试任务。"
            )]
            



async def fvt_agent_node(
    state: FVTState, 
    config: Optional[RunnableConfig] = None
) -> Command[Literal["__end__"]]:
    """FVT智能体节点函数
    
    工作流中的FVT节点入口点，负责创建和执行FVTSubAgent实例。
    
    Args:
        state: FVT工作流状态
        config: 运行时配置，包含系统配置信息
        
    Returns:
        Command[Literal["__end__"]]: 指向工作流结束的命令
    """
    try:
        # 从运行配置中提取系统配置
        configurable = Configuration.from_runnable_config(config)
        
        # 创建并执行FVT子智能体
        fvt_agent = FVTSubAgent(configurable)
        return await fvt_agent.execute(state, config)
        
    except Exception as e:
        logger.error(f"FVT节点执行失败: {e}")
        
        # 即使失败也要继续到结束节点
        return Command(goto="__end__")