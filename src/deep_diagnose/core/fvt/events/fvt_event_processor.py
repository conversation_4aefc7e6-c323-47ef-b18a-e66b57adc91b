"""FVT事件消息处理器

统一管理所有FVT相关消息处理器的协调器。
提供高效的消息分发和处理能力，支持按Agent类型过滤处理。
"""

import logging
from typing import Any, Dict, List, Optional

from deep_diagnose.core.events.storage import MessageRepository
from deep_diagnose.core.fvt.events.processors.fvt_processor import FVTMessageProcessor

logger = logging.getLogger(__name__)


class FVTEventMessageProcessor:
    """FVT事件消息处理器
    
    管理所有FVT相关消息处理器的协调器。
    负责将消息分发给相应的处理器并汇总结果。
    
    Attributes:
        request_id: 请求标识符
        processors: Agent名称到处理器的映射
    """

    def __init__(self, request_id: Optional[str] = None) -> None:
        """初始化FVT事件消息处理器
        
        Args:
            request_id: 请求ID，传递给所有子处理器
        """
        self.request_id = request_id
        self.processors = {
            "fvt": FVTMessageProcessor(request_id=request_id),
        }

    def process_business_messages(
        self, 
        message_repository: MessageRepository, 
        only_agents: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """处理业务消息
        
        根据指定的Agent列表过滤和处理消息，提高处理效率。
        
        Args:
            message_repository: 消息存储库
            only_agents: 要处理的Agent列表，None表示处理所有
            
        Returns:
            Dict[str, Any]: 汇总的处理结果
        """
        try:
            results = {}
            
            # 确定要处理的处理器
            target_processors = self._get_target_processors(only_agents)
            
            # 处理每个Agent的消息
            for agent_name, processor in target_processors:
                agent_results = self._process_agent_messages(
                    message_repository, agent_name, processor
                )
                if agent_results:
                    results.update(agent_results)
                    
            return results
            
        except Exception as e:
            logger.error(f"处理FVT业务消息失败: {e}", exc_info=True)
            return {}
            
    def _get_target_processors(self, only_agents: Optional[List[str]]) -> List[tuple]:
        """获取目标处理器列表
        
        Args:
            only_agents: 指定的Agent列表
            
        Returns:
            List[tuple]: (agent_name, processor)元组列表
        """
        if not only_agents:
            return list(self.processors.items())
            
        # 计算交集，只处理有效的Agent
        valid_agents = set(only_agents) & set(self.processors.keys())
        return [(agent, self.processors[agent]) for agent in valid_agents]
        
    def _process_agent_messages(
        self, 
        message_repository: MessageRepository, 
        agent_name: str, 
        processor: FVTMessageProcessor
    ) -> Dict[str, Any]:
        """处理单个Agent的消息
        
        Args:
            message_repository: 消息存储库
            agent_name: Agent名称
            processor: 对应的处理器
            
        Returns:
            Dict[str, Any]: 处理结果，失败时返回空字典
        """
        try:
            # 获取Agent的消息
            agent_messages = message_repository.get_messages_by_agent(agent_name)
            
            if not agent_messages:
                return {}
                
            # 处理消息
            result = processor.process_messages(agent_messages)
                
            return result
            
        except Exception as e:
            logger.warning(f"处理Agent {agent_name} 的消息失败: {e}")
            return {}

    def get_supported_agents(self) -> List[str]:
        """获取支持的Agent列表
        
        Returns:
            List[str]: 支持的Agent名称列表
        """
        return list(self.processors.keys())