"""FVT智能体事件模型

专门用于FVT功能验证测试的简化事件处理实现。
提供结构化的事件数据管理和异步消息处理能力。
"""

import asyncio
import json
import logging
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional

from deep_diagnose.common.constants.app import REQUEST_ID_FIELD
from deep_diagnose.core.events.base_event import BaseAgentOutputEvent
from deep_diagnose.core.events.collectors import MessageCollector
from deep_diagnose.core.events.storage import MessageRepository
from deep_diagnose.core.fvt.events.fvt_event_processor import FVTEventMessageProcessor

logger = logging.getLogger(__name__)


@dataclass
@BaseAgentOutputEvent.register_event_type("fvt_event")
class FVTAgentEvent(BaseAgentOutputEvent):
    """FVT智能体事件 - 简化的事件处理模型
    
    提供FVT功能验证测试的结构化事件数据管理。
    支持异步消息处理、状态更新和事件序列化。
    
    Attributes:
        understanding: 任务理解信息
        thought: 思考过程记录
        plan_steps: 执行计划步骤列表
        executions: 执行操作记录列表
        result: 最终执行结果
        urls: 相关URL链接列表
        model_outputs: 模型调用输出列表
        reasoning_contents: 推理内容列表
        tool_calls: 工具调用记录列表
        request_id: 请求标识符
        finished: 是否执行完成
    """

    # 业务数据字段
    understanding: str = ""
    thought: str = ""
    plan_steps: List[Dict[str, Any]] = field(default_factory=list)
    executions: List[Dict[str, Any]] = field(default_factory=list)
    result: str = ""
    urls: List[Dict[str, Any]] = field(default_factory=list)
    
    # 模型调用相关字段
    model_outputs: List[str] = field(default_factory=list)
    reasoning_contents: List[str] = field(default_factory=list)
    tool_calls: List[Dict[str, Any]] = field(default_factory=list)
    
    # 系统字段
    request_id: Optional[str] = None
    finished: bool = False
    
    # 内部字段
    _version: int = field(default=0, init=False)
    
    def __post_init__(self) -> None:
        """初始化事件处理组件"""
        self._init_message_components()
        self._init_async_components()
        
    def _init_message_components(self) -> None:
        """初始化消息处理组件"""
        self.message_repository = MessageRepository()
        self.collector = MessageCollector()
        self.business_processor = FVTEventMessageProcessor(request_id=self.request_id)
        
    def _init_async_components(self) -> None:
        """初始化异步处理组件"""
        self._queue: asyncio.Queue = asyncio.Queue(maxsize=1000)
        self._processor_task: Optional[asyncio.Task] = None
        self._stop_event = asyncio.Event()
        self._version_changed = asyncio.Event()

    def parse(self, raw_event: str) -> bool:
        """解析原始SSE事件
        
        Args:
            raw_event: 原始事件字符串
            
        Returns:
            bool: 始终返回False，表示不处理原始SSE事件
        """
        return False

    async def _processor_loop(self) -> None:
        """后台处理协程：串行消费消息队列"""
        
        while not self._stop_event.is_set():
            try:
                # 从队列中获取消息项
                item = await asyncio.wait_for(self._queue.get(), timeout=1.0)
                
                if item is None:  # 停止信号
                    break
                    
                # 处理消息项
                await self._process_queue_item(item)
                    
            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                logger.error(f"FVT事件处理循环异常: {e}", exc_info=True)

    async def _process_queue_item(self, item: tuple) -> None:
        """处理队列中的单个消息项"""
        try:
            message_chunk, message_metadata = item
            
            # 收集消息变更
            change = self.collector.collect_message(
                self.message_repository, 
                message_chunk, 
                message_metadata
            )
            
            if not change:
                return
                
            # 处理业务消息
            agents = [change.agent_name] if change.agent_name else []
            event_data = self.business_processor.process_business_messages(
                self.message_repository, 
                only_agents=agents
            )
            
            if event_data:
                self._apply_event_data(event_data)
                
        except Exception as e:
            logger.warning(f"处理队列项失败: {e}")

    def handle_message(self, message_chunk: Any, message_metadata: dict) -> bool:
        """处理单个消息
        
        Args:
            message_chunk: 消息内容
            message_metadata: 消息元数据
            
        Returns:
            bool: 处理成功返回True，否则返回False
        """
        try:
            # 启动后台处理任务
            self._ensure_processor_running()
            
            # 将消息加入队列
            try:
                self._queue.put_nowait((message_chunk, message_metadata))
                return True
            except asyncio.QueueFull:
                logger.warning("FVT处理器队列已满，丢弃消息")
                return False

        except Exception as e:
            logger.error(f"处理FVT消息失败: {e}", exc_info=True)
            return False
            
    def _ensure_processor_running(self) -> None:
        """确保后台处理任务正在运行"""
        if self._processor_task is None or self._processor_task.done():
            loop = asyncio.get_event_loop()
            self._processor_task = loop.create_task(self._processor_loop())

    def stop(self) -> None:
        """停止事件处理"""
        try:
            self._stop_event.set()
            if self._processor_task and not self._processor_task.done():
                self.finished = True
                # 发送停止信号
                try:
                    self._queue.put_nowait(None)
                except asyncio.QueueFull:
                    pass
        except Exception:
            pass

    def _apply_event_data(self, event_data: Dict[str, Any]) -> None:
        """应用事件数据到业务字段
        
        Args:
            event_data: 要应用的事件数据字典
        """
        for key, value in event_data.items():
            if hasattr(self, key):
                setattr(self, key, value)
            
        # 更新版本号
        self._version += 1
        
        # 通知版本变更
        try:
            self._version_changed.set()
        except RuntimeError:
            # 在某些情况下事件循环可能已关闭
            pass

    def parse_graph_event(self, agent_path: tuple, event_data: tuple, request_id: str) -> bool:
        """解析LangGraph事件
        
        处理来自LangGraph工作流的事件数据，更新内部状态。

        Args:
            agent_path: Agent路径元组，用于标识事件来源
            event_data: 事件数据元组，格式为(message_chunk, message_metadata)
            request_id: 请求标识符

        Returns:
            bool: 成功处理返回True，否则返回False
        """
        if not isinstance(event_data, tuple) or len(event_data) != 2:
            return False

        message_chunk, message_metadata = event_data

        # 确保元数据包含请求ID
        if isinstance(message_metadata, dict):
            metadata = message_metadata.copy()
            metadata[REQUEST_ID_FIELD] = request_id
        else:
            metadata = {REQUEST_ID_FIELD: request_id}

        return self.handle_message(message_chunk, metadata)

    def to_sse_format(self) -> str:
        """转换为SSE格式字符串
        
        将事件对象序列化为JSON字符串，用于SSE传输。
        保持与ReasoningAgentEvent相同的输出格式。
        
        Returns:
            str: JSON格式的事件数据字符串
        """
        # 只输出核心业务字段，与ReasoningAgentEvent保持一致
        event_data = {
            "understanding": self.understanding,
            "thought": self.thought,
            "plan_steps": self.plan_steps,
            "executions": self.executions,
            "result": self.result,
            "urls": self.urls,
            "request_id": self.request_id,
            "event_type": self.event_type,
            "finished": self.finished
        }

        return json.dumps(event_data, ensure_ascii=False)

    @classmethod
    def from_sse_format(cls, event_data: str) -> Optional['FVTAgentEvent']:
        """从SSE格式反序列化FVT事件
        
        从JSON字符串创建FVTAgentEvent实例。
        
        Args:
            event_data: JSON格式的事件数据字符串
            
        Returns:
            Optional[FVTAgentEvent]: 成功时返回事件实例，失败时返回None
        """
        try:
            data = json.loads(event_data)
            
            # 验证事件类型
            expected_type = "fvt_event"
            actual_type = data.get("event_type")
            if actual_type and actual_type != expected_type:
                logger.warning(
                    f"事件类型不匹配: 期望'{expected_type}', 实际'{actual_type}'"
                )
                return None
            
            # 创建事件实例并设置字段
            event = cls(request_id=data.get("request_id"))
            
            # 业务字段
            event.understanding = data.get("understanding", "")
            event.thought = data.get("thought", "")
            event.plan_steps = data.get("plan_steps", [])
            event.executions = data.get("executions", [])
            event.result = data.get("result", "")
            event.urls = data.get("urls", [])
            
            # 模型调用字段
            event.model_outputs = data.get("model_outputs", [])
            event.reasoning_contents = data.get("reasoning_contents", [])
            event.tool_calls = data.get("tool_calls", [])
            
            # 系统字段
            event.finished = data.get("finished", False)
            
            return event
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"从SSE格式解析FVT事件失败: {e}")
            return None