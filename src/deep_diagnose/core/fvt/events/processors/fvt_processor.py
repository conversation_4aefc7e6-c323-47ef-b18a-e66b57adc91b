"""FVT消息处理器

专门处理FVT Agent消息的核心处理器实现。
负责提取工具调用信息和构建标准化的FVT输出格式。
"""

import logging
from typing import Any, Dict, List, Optional

from deep_diagnose.core.events.models import AgentMessage

from .fvt_processor_base import FVTProcessorBase

logger = logging.getLogger(__name__)


class FVTMessageProcessor(FVTProcessorBase):
    """FVT消息处理器
    
    专门处理FVT（功能验证测试）相关的消息。
    在基类功能基础上增加工具调用提取和结果标准化处理。
    """

    def __init__(self, request_id: Optional[str] = None) -> None:
        """初始化FVT消息处理器
        
        Args:
            request_id: 请求ID，用于标识处理上下文
        """
        super().__init__(request_id=request_id)

    def get_agent_name(self) -> str:
        """获取代理名称
        
        Returns:
            str: 固定返回"fvt"
        """
        return "fvt"

    def process_messages(self, messages: List[AgentMessage]) -> Dict[str, Any]:
        """处理FVT消息的主入口方法
        
        在基类处理基础上，增加工具调用提取和结果标准化。
        
        Args:
            messages: 待处理的消息列表
            
        Returns:
            Dict[str, Any]: 标准化的FVT处理结果
        """
        try:
            # 使用基类处理基础逻辑
            base_result = super().process_messages(messages)
            if not base_result:
                return {}
            
            # 构建增强的FVT结果
            enhanced_result = self._enhance_result(base_result, messages)
            
            # 记录处理统计
            self._log_processing_stats(enhanced_result)
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"处理FVT消息失败: {e}", exc_info=True)
            return {}

    def _enhance_result(self, base_result: Dict[str, Any], messages: List[AgentMessage]) -> Dict[str, Any]:
        """增强基础结果，添加FVT特有的处理逻辑
        
        Args:
            base_result: 基类处理的基础结果
            messages: 原始消息列表
            
        Returns:
            Dict[str, Any]: 增强后的结果字典
        """
        # 复制基础结果
        result = base_result.copy()
        
        # 添加工具调用信息
        tool_calls = self._extract_tool_calls(messages)
        if tool_calls:
            result["tool_calls"] = tool_calls
        
        # 确保result字段存在（使用优先级策略）
        result = self._ensure_result_field(result)
        
        return result
        
    def _extract_tool_calls(self, messages: List[AgentMessage]) -> List[Dict[str, Any]]:
        """从消息中提取工具调用信息
        
        Args:
            messages: 消息列表
            
        Returns:
            List[Dict[str, Any]]: 工具调用信息列表
        """
        tool_calls = []
        
        for message in messages:
            if not message.tool_executions:
                continue
                
            for tool_execution in message.tool_executions:
                tool_call = {
                    "call_name": tool_execution.call_name,
                    "arguments": tool_execution.arguments,
                    "output": tool_execution.output
                }
                tool_calls.append(tool_call)
                
        return tool_calls
        
    def _ensure_result_field(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """确保结果字典包含result字段
        
        使用优先级策略：result > model_outputs > thought
        
        Args:
            result: 待处理的结果字典
            
        Returns:
            Dict[str, Any]: 包含result字段的结果字典
        """
        if "result" in result and result["result"]:
            return result
            
        # 尝试使用模型输出作为result
        if "model_outputs" in result and result["model_outputs"]:
            result["result"] = "\n".join(result["model_outputs"])
            return result
            
        # 最后尝试使用thought作为result
        if "thought" in result and result["thought"]:
            result["result"] = result["thought"]
            
        return result
        
    def _log_processing_stats(self, result: Dict[str, Any]) -> None:
        """记录处理统计信息
        
        Args:
            result: 处理结果字典
        """
        field_count = len(result)
        logger.info(f"FVT消息处理完成，生成 {field_count} 个字段")
        
        # 记录关键统计信息
        stats = []
        
        if "executions" in result:
            stats.append(f"{len(result['executions'])} 个执行操作")
            
        if "tool_calls" in result:
            stats.append(f"{len(result['tool_calls'])} 个工具调用")
            
        if "model_outputs" in result:
            total_chars = sum(len(output) for output in result["model_outputs"])
            stats.append(f"{len(result['model_outputs'])} 个模型输出({total_chars} 字符)")
            
        if "result" in result:
            stats.append(f"最终结果({len(result['result'])} 字符)")
            
        if stats:
            logger.info(f"FVT处理统计: {', '.join(stats)}")