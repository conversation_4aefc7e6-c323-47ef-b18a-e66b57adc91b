"""FVT处理器基类

为FVT相关处理器提供统一的消息处理逻辑和通用方法。
支持多种内容解析格式和标准化的结果构建。
"""

import json
import logging
from abc import abstractmethod
from typing import Any, Dict, List

from deep_diagnose.core.events.models import AgentMessage
from deep_diagnose.core.events.processors.base_processor import BaseMessageProcessor

logger = logging.getLogger(__name__)


class FVTProcessorBase(BaseMessageProcessor):
    """FVT处理器基类
    
    为FVT相关的消息处理器提供统一的基础功能。
    包括消息过滤、内容解析和结果构建等通用逻辑。
    
    Attributes:
        request_id: 请求标识符，用于跟踪处理上下文
    """

    def __init__(self, request_id: str = None) -> None:
        """初始化FVT处理器
        
        Args:
            request_id: 请求ID，用于标识处理上下文
        """
        super().__init__(request_id=request_id)

    @abstractmethod
    def get_agent_name(self) -> str:
        """获取代理名称
        
        Returns:
            str: 代理名称，用于标识处理器类型
        """
        pass

    def process_messages(self, messages: List[AgentMessage]) -> Dict[str, Any]:
        """处理FVT消息的主入口方法
        
        过滤完成的消息，解析内容并构建标准化结果。
        
        Args:
            messages: 待处理的消息列表
            
        Returns:
            Dict[str, Any]: 处理结果字典，包含各种业务字段
        """
        try:
            # 过滤已完成的消息
            finished_messages = [msg for msg in messages if msg.is_finished]
            
            if not finished_messages:
                return {}
            
            # 解析消息内容
            parsed_data = self._extract_message_data(finished_messages)
            
            # 构建标准化结果
            result = self._build_result(parsed_data)
            
            logger.info(f"FVT消息处理完成，生成字段: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"处理FVT消息失败: {e}", exc_info=True)
            return {}
            
    def _extract_message_data(self, messages: List[AgentMessage]) -> Dict[str, List]:
        """从消息列表中提取各种数据
        
        Args:
            messages: 已完成的消息列表
            
        Returns:
            Dict[str, List]: 包含各种提取数据的字典
        """
        data = {
            "thoughts": [],
            "executions": [],
            "results": [],
            "model_outputs": [],
            "reasoning_contents": []
        }
        
        for i, message in enumerate(messages):
            
            # 处理原始内容
            if message.content.strip():
                content = message.content.strip()
                data["model_outputs"].append(content)
                
                # 解析FVT特定格式
                thought, executions, result = self._parse_fvt_content(content)
                
                if thought.strip():
                    data["thoughts"].append(thought.strip())
                if executions:
                    data["executions"].extend(executions)
                if result.strip():
                    data["results"].append(result.strip())
                        
            # 处理推理内容
            if message.reasoning_content.strip():
                data["reasoning_contents"].append(
                    message.reasoning_content.strip()
                )
                
        return data
        
    def _build_result(self, data: Dict[str, List]) -> Dict[str, Any]:
        """从提取的数据构建最终结果
        
        Args:
            data: 提取的数据字典
            
        Returns:
            Dict[str, Any]: 构建的结果字典
        """
        result = {}
        
        # 处理思考内容
        if data["thoughts"]:
            result["thought"] = "\n".join(data["thoughts"])
            
        # 处理执行记录
        if data["executions"]:
            result["executions"] = data["executions"]
            
        # 处理结果内容
        if data["results"]:
            result["result"] = "\n".join(data["results"])
            
        # 处理模型输出
        if data["model_outputs"]:
            result["model_outputs"] = data["model_outputs"]
            
            # 如果没有明确结果，使用模型输出
            if "result" not in result:
                combined_output = "\n".join(data["model_outputs"])
                result["result"] = combined_output
                
        # 处理推理内容
        if data["reasoning_contents"]:
            result["reasoning_contents"] = data["reasoning_contents"]
            
        return result

    def _parse_fvt_content(self, content: str) -> tuple:
        """解析FVT内容
        
        支持JSON和纯文本两种格式的内容解析。
        
        Args:
            content: 待解析的内容字符串
            
        Returns:
            tuple: (思考内容, 执行列表, 结果内容)
        """
        # 首先尝试JSON解析
        json_result = self._try_parse_json_content(content)
        if json_result is not None:
            return json_result
            
        # JSON解析失败，使用文本解析
        return self._parse_text_content(content)

    def _try_parse_json_content(self, content: str) -> tuple:
        """尝试JSON格式解析
        
        Args:
            content: JSON格式的内容字符串
            
        Returns:
            tuple: 解析结果元组，失败时返回None
        """
        try:
            data = json.loads(content)
            
            if not isinstance(data, dict):
                return None
                
            # 提取标准字段
            thought = data.get("thought", "")
            executions = data.get("executions", [])
            result = data.get("result", "")
            
            # 回退策略：如果没有thought，尝试从content字段获取
            if not thought and "content" in data:
                thought = str(data["content"])
                
            return thought, executions, result
            
        except json.JSONDecodeError:
            return None
        except Exception as e:
            return None

    def _parse_text_content(self, content: str) -> tuple:
        """解析纯文本格式的FVT内容
        
        使用简单的行分割策略解析文本内容。
        
        Args:
            content: 纯文本内容字符串
            
        Returns:
            tuple: (思考内容, 空执行列表, 结果内容)
        """
        try:
            lines = content.strip().split('\n')
            if not lines or not lines[0].strip():
                return "", [], ""
                
            # 第一行作为思考内容
            thought = lines[0].strip()
            
            # 多行时，剩余行作为结果内容
            if len(lines) > 1:
                result = '\n'.join(lines[1:]).strip()
            else:
                result = ""
                
            return thought, [], result

        except Exception as e:
            logger.warning(f"解析文本FVT内容失败: {e}")
            return "", [], ""