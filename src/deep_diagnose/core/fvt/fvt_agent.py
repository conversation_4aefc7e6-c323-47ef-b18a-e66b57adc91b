"""
FVT智能体主类实现

参考ReasoningAgent的简化实现，专门用于功能验证测试。
提供统一的FVT测试执行接口，支持流式事件输出和状态管理。
"""

import asyncio
import logging
from typing import Any, AsyncGenerator, Dict, List, Optional
from uuid import uuid4

from deep_diagnose.common.config import get_config
from deep_diagnose.core.agent.base_graph import BaseGraph
from deep_diagnose.core.fvt.events.fvt_events import FVTAgentEvent
from deep_diagnose.core.fvt.workflow.builder import build_fvt_graph_with_memory

logger = logging.getLogger(__name__)


class FVTAgent(BaseGraph):
    """FVT功能验证测试智能体
    
    简化的智能体实现，专门用于功能验证测试场景。
    支持流式事件输出、状态管理和可观测性集成。
    
    Attributes:
        _graph: FVT工作流图实例
        _langfuse_handler: Langfuse可观测性处理器  
        request_id: 当前请求ID
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None) -> None:
        """初始化FVT智能体
        
        Args:
            config: 配置字典，可包含以下参数:
                - langfuse_handler: 外部提供的Langfuse处理器
                - request_id: 初始请求ID
        """
        super().__init__(config)
        self._graph = build_fvt_graph_with_memory()
        self._config = config or {}
        
        # 初始化可观测性组件
        self._langfuse_handler = self._config.get("langfuse_handler")
        
        # 初始化运行时状态
        self.request_id = self._config.get("request_id", "")

    async def astream(
            self,
            question: str,
            messages: Optional[List[Dict[str, Any]]] = None,
            **kwargs: Any
    ) -> AsyncGenerator[FVTAgentEvent, None]:
        """流式执行FVT测试任务并输出结构化事件
        
        Args:
            question: 用户问题
            messages: 历史消息列表
            **kwargs: 其他参数，支持request_id和thread_id
            
        Yields:
            FVTAgentEvent: FVT事件对象
        """
        request_id = self._extract_request_id(kwargs)
        logger.info(f"FVTAgent starting execution - question: '{question}', request_id: {request_id}")
        
        fvt_event = None
        try:
            # 准备输入数据
            input_data = self._prepare_input_data(question, messages, request_id)
            config = self._prepare_config(request_id)
            
            # 创建并初始化事件对象
            fvt_event = FVTAgentEvent(request_id=request_id)
            fvt_event.understanding = "已接收FVT测试请求，正在启动功能验证测试流程"
            yield self._create_event_copy(fvt_event)

            # 执行工作流并处理事件流
            async for event_copy in self._execute_workflow_stream(input_data, config, fvt_event):
                yield event_copy

        except Exception as e:
            logger.error(f"FVTAgent执行失败: {e}", exc_info=True)
            yield self._create_error_event(request_id, str(e))

        finally:
            # 清理资源
            if fvt_event:
                fvt_event.stop()

    def _extract_request_id(self, kwargs: Dict[str, Any]) -> str:
        """从参数中提取或生成请求ID"""
        request_id = kwargs.get("request_id") or kwargs.get("thread_id", "fvt_run")
        return str(uuid4()) if (not request_id or request_id == "fvt_run") else request_id

    def _prepare_input_data(self, question: str, messages: Optional[List[Dict[str, Any]]], request_id: str) -> Dict[str, Any]:
        """准备工作流输入数据"""
        if not messages:
            messages = [{"role": "user", "content": question}]
        
        return {
            "messages": messages,
            "request_id": request_id,
        }

    def _prepare_config(self, request_id: str) -> Dict[str, Any]:
        """准备工作流配置"""
        config = {"thread_id": request_id}
        
        # 添加可观测性处理器
        langfuse_handler = self._langfuse_handler or self._create_langfuse_handler()
        if langfuse_handler:
            config["callbacks"] = [langfuse_handler]
            
        return config

    async def _execute_workflow_stream(
        self, 
        input_data: Dict[str, Any], 
        config: Dict[str, Any], 
        fvt_event: FVTAgentEvent
    ) -> AsyncGenerator[FVTAgentEvent, None]:
        """执行工作流并处理事件流"""
        critical_agents = {"fvt"}
        
        async for agent, _, event_data in self._graph.astream(
            input_data,
            config=config,
            stream_mode=["messages", "updates"],
            subgraphs=True,
        ):
            # 提取agent名称
            agent_name = self._extract_agent_name(agent)
            
            if agent_name not in critical_agents:
                continue

            # 处理事件并输出副本
            if self._process_graph_event(fvt_event, agent, event_data, input_data["request_id"]):
                yield self._create_event_copy(fvt_event)

        # 标记完成并输出最终事件
        fvt_event.finished = True
        
        # 只有在结果为空时才设置默认完成消息
        if not fvt_event.result.strip():
            fvt_event.result = "FVT功能验证测试已完成"
            
        yield self._create_event_copy(fvt_event)

    def _extract_agent_name(self, agent: Any) -> str:
        """提取agent名称"""
        if isinstance(agent, tuple) and len(agent) > 0:
            agent_str = str(agent[0])
        else:
            agent_str = str(agent)
        
        return agent_str.split(':')[0] if ':' in agent_str else agent_str

    def _process_graph_event(self, fvt_event: FVTAgentEvent, agent: Any, event_data: Any, request_id: str) -> bool:
        """处理图事件"""
        try:
            # 解析事件并更新状态
            result = fvt_event.parse_graph_event(agent, event_data, request_id)
            
            # 简单检查队列状态，不进行异步等待
            # 因为这个方法是同步的，异步等待会造成问题
                    
            return result
        except Exception as e:
            logger.warning(f"处理图事件失败: {e}")
            return False

    def _create_event_copy(self, event: FVTAgentEvent) -> FVTAgentEvent:
        """创建事件副本"""
        new_event = FVTAgentEvent(request_id=event.request_id)
        new_event.understanding = event.understanding
        new_event.thought = event.thought
        new_event.plan_steps = event.plan_steps.copy()
        new_event.executions = event.executions.copy()
        new_event.result = event.result
        new_event.urls = event.urls.copy()
        new_event.finished = event.finished
        new_event._version = event._version
        return new_event

    def _create_error_event(self, request_id: str, error_message: str) -> FVTAgentEvent:
        """创建错误事件"""
        error_event = FVTAgentEvent(request_id=request_id)
        error_event.result = f"FVT执行失败: {error_message}"
        error_event.finished = True
        return error_event

    def _create_langfuse_handler(self) -> Optional[Any]:
        """创建Langfuse处理器
        
        遵循Langfuse处理器兼容性处理规范，支持可观测性跟踪。
        
        Returns:
            Langfuse处理器实例，创建失败时返回None
        """
        try:
            # 延迟导入以避免模块导入时的ImportError
            from langfuse.callback import CallbackHandler  # type: ignore
            
            config = get_config()
            handler = CallbackHandler(
                public_key=config.observability.langfuse.public_key,
                secret_key=config.observability.langfuse.secret_key,
                host=config.observability.langfuse.endpoint
            )
            
            logger.info("Langfuse处理器创建成功")
            return handler
            
        except ImportError:
            return None
        except Exception as e:
            logger.warning(f"创建Langfuse处理器失败: {e}")
            return None