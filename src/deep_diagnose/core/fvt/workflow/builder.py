"""FVT工作流构建器

提供FVT功能验证测试工作流的构建功能。
支持带内存和不带内存两种模式的工作流图构建。
"""

import logging
from typing import Any

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph

from .types import FVTState

logger = logging.getLogger(__name__)


def _build_base_graph() -> StateGraph:
    """构建基础的FVT状态图
    
    创建包含单个FVT节点的线性工作流图。
    
    Returns:
        StateGraph: 配置好的状态图构建器
        
    Raises:
        Exception: 当图构建失败时抛出异常
    """
    try:
        # 延迟导入以避免循环依赖
        from ..agents import fvt_agent_node
        
        builder = StateGraph(FVTState)
        
        # 添加FVT处理节点
        builder.add_node("fvt", fvt_agent_node)
        
        # 构建线性流程：START -> fvt -> END
        builder.add_edge(START, "fvt")
        builder.add_edge("fvt", END)
        
        return builder
        
    except Exception as e:
        logger.error(f"构建FVT基础图失败: {e}")
        raise


def build_fvt_graph_with_memory() -> Any:
    """构建带有内存的FVT工作流图
    
    创建支持状态持久化的FVT工作流，适用于需要跨会话保持状态的场景。
    
    Returns:
        CompiledStateGraph: 编译好的带内存工作流图
        
    Raises:
        Exception: 当工作流编译失败时抛出异常
    """
    try:
        memory = MemorySaver()
        builder = _build_base_graph()
        compiled_graph = builder.compile(checkpointer=memory)
        
        logger.info("带内存的FVT工作流图构建成功")
        return compiled_graph
        
    except Exception as e:
        logger.error(f"构建带内存的FVT工作流图失败: {e}")
        raise


def build_fvt_graph() -> Any:
    """构建不带内存的FVT工作流图
    
    创建无状态的FVT工作流，适用于单次执行场景。
    
    Returns:
        CompiledStateGraph: 编译好的无状态工作流图
        
    Raises:
        Exception: 当工作流编译失败时抛出异常
    """
    try:
        builder = _build_base_graph()
        compiled_graph = builder.compile()
        
        logger.info("无状态FVT工作流图构建成功")
        return compiled_graph
        
    except Exception as e:
        logger.error(f"构建FVT工作流图失败: {e}")
        raise


# 默认graph变量，延迟初始化以避免循环导入
graph = None

def get_default_graph():
    """获取默认的FVT工作流图
    
    延迟初始化默认图实例，避免模块加载时的循环导入问题。
    
    Returns:
        CompiledStateGraph: 默认的FVT工作流图
    """
    global graph
    if graph is None:
        try:
            graph = build_fvt_graph()
        except Exception as e:
            logger.error(f"默认FVT工作流图初始化失败: {e}")
            raise
    return graph