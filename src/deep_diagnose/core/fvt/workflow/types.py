"""FVT状态模型定义

定义FVT工作流的状态结构，为功能验证测试提供完整的上下文信息。
继承自MessagesState以支持消息传递，并扩展了观察信息和运行时状态。
"""

from typing import List

from langgraph.graph import MessagesState


class FVTState(MessagesState):
    """FVT工作流状态模型
    
    扩展了MessagesState以支持FVT特定的状态信息。
    包含消息历史和运行时配置等核心数据。
    
    Attributes:
        messages: 继承自MessagesState的消息列表
        request_id: 请求标识符，用于跟踪和调试
        locale: 本地化设置，默认为中文
        
    Example:
        >>> state = FVTState()
        >>> state.request_id = "fvt-test-001"
    """
    
    # 运行时状态字段
    request_id: str = ""  # 请求唯一标识符
    locale: str = "zh-CN"  # 本地化设置，影响输出语言