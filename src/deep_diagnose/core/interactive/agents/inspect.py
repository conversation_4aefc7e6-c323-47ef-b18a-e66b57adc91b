"""
Quick inspect of a machine, include: instance, host
"""

import asyncio
import copy
import json
import logging
import time
from typing import AsyncIterator, Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

from deep_diagnose.common.utils.machine_utils import MachineIdUtil, MachineIdType
from deep_diagnose.core.events.inspect_event import InspectEvent
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager
from deep_diagnose.storage.redis_client import RedisClient
from deep_diagnose.llms.app_bailian import app_bailian, BailianAppEnum
from deep_diagnose.domain.chat.models import CloudbotAgentChatMessage, MessageType, MessageSource, MessageStatus
from deep_diagnose.core.agent.base_graph import BaseGraph
from deep_diagnose.core.interactive.types.state import InspectState

logger = logging.getLogger(__name__)


class InspectAgent(BaseGraph):
    def __init__(self, config):
        super().__init__(config)

        # MCP工具白名单，包含了一系列允许执行的操作或功能
        self.mcp_tool_whitelist = [
            "getVmBasicInfo",
            "getNcBasicInfo",
            "listCategorizedMonitorExceptions",
            "listReportedOperationalEvents",
            "listOperationRecords",
        ]

        self.redis_client = RedisClient()

    async def astream(self, question: str, messages: Optional[List[Dict[str, Any]]] = None, **kwargs) -> AsyncIterator[InspectEvent]:
        """Main execution flow: event driven mode"""
        additional_info = kwargs.get("additional_info", {})

        # Create initial event object
        inspect_event = InspectEvent()
        inspect_event.set_machine_info(
            additional_info.get("machine_id", ""),
            additional_info.get("start_time", ""),
            additional_info.get("end_time", ""),
            MachineIdUtil.get_machine_id_type(additional_info.get("machine_id", "")),
        )

        # Initialize execution state
        state = InspectState(
            machine_id=additional_info.get("machine_id", ""),
            start_time=additional_info.get("start_time", ""),
            end_time=additional_info.get("end_time", ""),
            machine_id_type=MachineIdUtil.get_machine_id_type(kwargs.get("machine_id", "")),
            tool_map={},
            info_data={},
            finished=False,
            recommendations=[],
            overview="",
        )

        # Set default time
        if state.start_time == "":
            state.start_time = (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d %H:%M:%S")
        if state.end_time == "":
            state.end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        state.machine_id_type = MachineIdUtil.get_machine_id_type(state.machine_id)

        # Get MCP tools
        mcp_tools = await MCPToolManager().get_enabled_mcp_tools()
        state.tool_map = {tool.name: tool for tool in mcp_tools if tool.name in self.mcp_tool_whitelist}

        logger.info(f"Tool map: {list(state.tool_map.keys())}")

        # Create event queue for main flow and event streaming
        event_queue = asyncio.Queue()

        # Start main flow
        main_task = asyncio.create_task(self._main_flow_with_events(state, event_queue))

        # Push initial event
        yield copy.deepcopy(inspect_event)

        try:
            async for event in self._event_driven_monitor(main_task, event_queue, inspect_event):
                yield event
        except Exception as e:
            logger.error(f"Inspect agent task failed: {e}", exc_info=True)
            main_task.cancel()
            inspect_event.finished = True
            yield inspect_event
        finally:
            state.finished = True

    async def _event_driven_monitor(self, main_task: asyncio.Task, event_queue: asyncio.Queue, inspect_event: InspectEvent) -> AsyncIterator[InspectEvent]:
        """Event driven monitor"""

        while not main_task.done():
            try:
                # Wait for updates in the event queue, with a timeout to avoid infinite waiting
                update = await asyncio.wait_for(event_queue.get(), timeout=0.1)

                # Apply updates to the event object
                updated = False
                for key, value in update.items():
                    if hasattr(inspect_event, key) and getattr(inspect_event, key) != value:
                        setattr(inspect_event, key, value)
                        updated = True

                # If there are updates, push the event
                if updated:
                    yield copy.deepcopy(inspect_event)
                    logger.debug(f"Event pushed with updates: {list(update.keys())}")

            except asyncio.TimeoutError:
                # Timeout is normal, continue looping
                continue
            except Exception as e:
                logger.error(f"Error in event monitor: {e}")
                break

        # Wait for the main task to complete
        try:
            await main_task
        except Exception as e:
            logger.error(f"Main task failed: {e}")

        # Ensure all remaining events in the queue are processed
        while not event_queue.empty():
            try:
                update = event_queue.get_nowait()
                for key, value in update.items():
                    if hasattr(inspect_event, key):
                        setattr(inspect_event, key, value)
            except asyncio.QueueEmpty:
                break

        # Push the final event
        inspect_event.finished = True
        yield inspect_event
        logger.info("Inspect agent finished, final event pushed")

    async def _main_flow_with_events(self, state: InspectState, event_queue: asyncio.Queue) -> None:
        """Main flow with event pushing"""

        logger.info(f"Main flow start for machine: {state.machine_id}")

        try:
            # Step 1: Information extraction - push progress event
            await self._push_event(event_queue, {"overview": "正在提取机器信息..."})
            logger.info(f"Extract information for machine: {state.machine_id}")
            await self._concurrent_query(state)

            # Update information data
            await self._push_event(event_queue, {"info_data": dict(state.info_data)})

            # Step 2: LLM call - push progress event
            await self._push_event(event_queue, {"overview": "正在生成状态概览..."})
            logger.info(f"Concurrent API calls for machine: {state.machine_id}")
            await self._concurrent_api_calls_with_events(state, event_queue)

            # Push final result
            await self._push_event(event_queue, {"overview": state.overview, "recommendations": list(state.recommendations), "finished": True})

        except Exception as e:
            logger.error(f"Inspect agent main flow failed: {e}", exc_info=True)
            await self._push_event(event_queue, {"overview": f"执行失败: {str(e)}", "finished": True})
        finally:
            # Mark execution as complete
            state.finished = True

    async def _push_event(self, event_queue: asyncio.Queue, update: Dict[str, Any]) -> None:
        """Push event updates to the queue"""
        try:
            await event_queue.put(update)
        except Exception as e:
            logger.error(f"Failed to push event: {e}")

    async def _concurrent_api_calls_with_events(self, state: InspectState, event_queue: asyncio.Queue) -> None:
        """Concurrent API calls with event pushing"""

        logger.info(f"Starting Bailian API calls for recommendation and overview generation, machine ID: {state.machine_id}")
        api_start_time = time.time()

        prompt = self._prompt_generate(state)

        # Create API call tasks
        recommendation_result = app_bailian.app_call(BailianAppEnum.recommendation_generator, prompt + "\n 上述信息推荐的进一步问题有：")
        overview_result = app_bailian.app_stream_call(BailianAppEnum.overview_generator, prompt + "\n 上述信息的查询资源状态总结：")

        # Process streaming overview generation, push updates in real-time
        async def process_overview():
            overview_start_time = time.time()
            overview_char_count = 0
            async for result, _ in overview_result:
                if result:
                    state.overview += result
                    overview_char_count += len(result)
                    # Push overview updates in real-time
                    await self._push_event(event_queue, {"overview": state.overview})

            overview_end_time = time.time()
            overview_duration = overview_end_time - overview_start_time
            logger.info(f"Inspect generation completed, duration: {overview_duration:.3f}s, generated characters: {overview_char_count}")

        # Process recommendation generation
        async def process_recommendation():
            recommendation_start_time = time.time()
            recommendation_result_text = await recommendation_result
            recommendation_end_time = time.time()
            recommendation_duration = recommendation_end_time - recommendation_start_time
            logger.info(f"Recommendation generation completed, duration: {recommendation_duration:.3f}s")
            return recommendation_result_text

        # Concurrent wait for both tasks to complete
        _, recommendation_result_text = await asyncio.gather(process_overview(), process_recommendation())

        api_end_time = time.time()
        total_api_duration = api_end_time - api_start_time
        logger.info(f"Total Bailian API call duration: {total_api_duration:.3f}s")

        # Parse recommendation results
        parse_start_time = time.time()
        default_recommendations = [
            {"content": "运维查询", "url": f"https://cloudbot2.aliyun-inc.com/cloudbot/ng2/#/ecs/ops/maintenance/search?type=vmName&vmName={state.machine_id}"},
            {
                "content": "资源详情查询",
                "url": f"https://changeplus.aliyun-inc.com/#/moon-main-operation/instanceOperation/detail?instanceId={state.machine_id}",
            },
        ]
        recommendations = []

        # 处理推荐结果
        if recommendation_result_text is None:
            logger.warning("Recommendation result is empty, using default recommendations")
            recommendations = default_recommendations
        else:
            recommendation_text = recommendation_result_text.split("\n")
            logger.info(f"Recommendation result raw text lines: {len(recommendation_text)}")

            for line in recommendation_text:
                if line.startswith("-") and "(" in line and ")" in line:
                    content_end = line.rfind("(")
                    if content_end > 1:
                        recommendation = line[1:content_end].strip()
                        time_range = line[content_end + 1 : -1]
                        recommendations.append({"content": recommendation, "time_range": time_range, "raw_content": line[1:].strip()})

            if len(recommendation_text) == 1 and "无" in recommendation_text[0]:
                logger.info("Detected 'no recommendation' indicator, using default recommendations")
                recommendations = default_recommendations

        # 更新状态并推送推荐
        state.recommendations = recommendations
        await self._push_event(event_queue, {"recommendations": list(state.recommendations)})

        parse_end_time = time.time()
        parse_duration = parse_end_time - parse_start_time
        logger.info(f"Recommendation parsing completed, duration: {parse_duration:.3f}s, parsed {len(recommendations)} recommendations")

    @staticmethod
    async def _state_monitor(state: InspectState, event: InspectEvent) -> AsyncIterator[str]:
        """监控执行状态"""
        while not state.finished:
            # Only send events when there are changes, reduce redundant pushes
            changed = False
            if event.finished != state.finished:
                event.finished = state.finished
                changed = True
            if event.overview != state.overview:
                event.overview = state.overview
                changed = True
            if event.recommendations != state.recommendations:
                event.recommendations = list(state.recommendations)  # Avoid reference issues
                changed = True
            if event.info_data != state.info_data:
                event.info_data = dict(state.info_data)
                changed = True
            if changed:
                yield event.to_sse_format()
                logger.info(f"Event pushed with updates: {list(event.to_dict().keys())}")
            await asyncio.sleep(0.5)
        # Last ensure the final state is pushed
        event.finished = state.finished
        event.overview = state.overview
        event.recommendations = list(state.recommendations)
        event.info_data = dict(state.info_data)
        yield event.to_sse_format()
        logger.info("Inspect agent finished, final event pushed")

    def _prompt_generate(self, state: InspectState) -> str:

        def construct_vminfo_prompt(data):
            required_fields = ["instanceId", "isLocalDisk", "imageName", "instanceType", "isWin", "osVersion", "ecsBusinessStatus", "status"]

            available_info = {}
            for field in required_fields:
                if field in data and data[field] is not None and data[field] != "":
                    available_info[field] = data[field]

            prompt_parts = []
            field_descriptions = {
                "instanceId": lambda v: f"实例ID为 {v}",
                "isLocalDisk": lambda v: f"磁盘类型是 {v}",
                "imageName": lambda v: f"镜像名称为 {v}",
                "instanceType": lambda v: f"实例规格为 {v}",
                "isWin": lambda v: f"是 {v} 操作系统",
                "osVersion": lambda v: f"操作系统版本是 {v}",
                "ecsBusinessStatus": lambda v: f"业务状态为 {v}",
                "status": lambda v: f"当前运行状态为 {v}",
            }

            for field in required_fields:
                if field in available_info:
                    prompt_parts.append(field_descriptions[field](available_info[field]))

            if prompt_parts:
                prompt = "，".join(prompt_parts) + "。"
            else:
                prompt = "无有效信息。"

            return prompt

        def construct_ncinfo_prompt(data):
            # Specify required fields
            required_fields = [
                "isLocalDisk",
                "cpuModel",
                "lockType",
                "productName",
                "multiCnNum",
                "cpuGeneration",
                "osType",
                "bizStatus",
                "dragonboxNc",
                "vcpuMod",
                "physicalModel",
                "lockReason",
                "grayBizType",
            ]

            # Field Chinese description mapping (for generating natural language)
            field_descriptions = {
                "isLocalDisk": lambda v: f"磁盘类型是 {v}",
                "cpuModel": lambda v: f"CPU型号为 {v}",
                "lockType": lambda v: f"锁定类型为 {v}",
                "productName": lambda v: f"产品名称为 {v}",
                "multiCnNum": lambda v: f"多CN节点数量为 {v}",
                "cpuGeneration": lambda v: f"CPU代数为 {v}",
                "osType": lambda v: f"操作系统类型为 {v}",
                "bizStatus": lambda v: f"业务状态为 {v}",
                "dragonboxNc": lambda v: f"是否为dragonBox架构：{v}",
                "vcpuMod": lambda v: f"vCPU模式为 {v}",
                "physicalModel": lambda v: f"物理机型为 {v}",
                "lockReason": lambda v: f"锁定原因为 {v}",
                "grayBizType": lambda v: f"是否为Gamma测试机：{v}",
            }

            # Extract existing valid fields (non-empty and non-None)
            prompt_parts = []
            for field in required_fields:
                value = data.get(field)
                if value is not None and value != "" and field in field_descriptions:
                    prompt_parts.append(field_descriptions[field](value))

            # Combine into a complete prompt
            if prompt_parts:
                prompt = "，".join(prompt_parts) + "。"
            else:
                prompt = "无有效信息。"

            return prompt

        def construct_event_prompt(data):
            """
            Client event prompt generator
            """
            if data.__len__() == 0:
                return "无需要客户知会或处理的运维事件\n"
            required_fields = ["instance", "reason", "code_desc", "status", "plan", "publish", "end"]
            field_descriptions = {
                "instance": lambda v: f"影响实例 {v}",
                "reason": lambda v: f"异常原因为 {v}",
                "code_desc": lambda v: f"处理动作为 {v}",
                "status": lambda v: f"当前状态为 {v}",
                "publish": lambda v: f"通知发布时间为 {v}",
                "plan": lambda v: f"计划处理时间为 {v}",
                "end": lambda v: f"实际完成时间为 {v}",
            }

            prompt_lines = ["以下是最近推送给客户的运维事件："]
            for event in data:
                event_lines = []
                for field in required_fields:
                    value = event.get(field)
                    if value is not None and value != "" and field in field_descriptions:
                        event_lines.append(field_descriptions[field](value))
                if event_lines:
                    prompt_lines.append("\n".join(event_lines))
                else:
                    prompt_lines.append("（无详细字段信息）")
                prompt_lines.append("")  # 空行分隔事件
            prompt = "\n".join(prompt_lines)
            return prompt

        def construct_exception_prompt(data):
            if data.__len__() == 0:
                return "时间范围内资源无监控异常\n"
            keymetric = data.get("keymetric", [])
            other = data.get("other", [])
            low_warning = data.get("low_warning", [])

            prompt = "时间范围内出现监控异常：\n"
            if keymetric.__len__() > 0:
                prompt += "关键监控异常：\n"
                for exception in keymetric:
                    prompt += f"""检测到异常 {exception.get('exceptionName', '')} ({exception.get('exceptionDesc', '')}): 在时间 {exception.get('exceptionTime', '')} - {exception.get('lastExceptionTime', '')} 共发生了 {exception.get('exceptionCount')}次。 详细原因解释为 {exception.get('reason')} + {exception.get('additionalInfo')}\n"""

            if other.__len__() > 0:
                prompt += "其他监控异常：\n"
                for exception in other:
                    prompt += f"""检测到其他异常 {exception.get('exceptionName', '')} ({exception.get('exceptionDesc', '')}): 在时间 {exception.get('exceptionTime', '')} - {exception.get('lastExceptionTime', '')} 共发生了 {exception.get('exceptionCount')}次。 详细原因解释为 {exception.get('reason')} + {exception.get('additionalInfo')}\n"""
            if low_warning.__len__() > 0:
                prompt += "低级别监控异常：\n"
                for exception in low_warning:
                    prompt += f"""检测到异常事件 {exception.get('exceptionName', '')} ({exception.get('exceptionDesc', '')}): 在时间 {exception.get('exceptionTime', '')} - {exception.get('lastExceptionTime', '')} 共发生了 {exception.get('exceptionCount')}次。 详细原因解释为 {exception.get('reason')} + {exception.get('additionalInfo')}\n"""

            return prompt

        def construct_operation_prompt(data):
            if data.__len__() == 0:
                return "时间范围内资源无运维规则命中记录\n"

            return ""

        """Generate prompt"""
        prompt = "查询资源类型为"
        if state.machine_id_type == MachineIdType.INSTANCE_ID:
            vm_info = state.info_data.get("getVmBasicInfo", [{}])
            base_info = vm_info[0] if isinstance(vm_info, list) and len(vm_info) > 0 else {}
            prompt += construct_vminfo_prompt(base_info)
        elif state.machine_id_type in [MachineIdType.NC_IP, MachineIdType.NC_ID]:
            nc_info = state.info_data.get("getNcBasicInfo", [{}])
            base_info = nc_info[0] if isinstance(nc_info, list) and len(nc_info) > 0 else {}
            prompt += construct_ncinfo_prompt(base_info)
        else:
            prompt += "机器类型未知。"

        prompt += construct_event_prompt(state.info_data.get("listReportedOperationalEvents", []))

        prompt += construct_exception_prompt(state.info_data.get("listCategorizedMonitorExceptions", []))

        prompt += construct_operation_prompt(state.info_data.get("listOperationRecords", []))

        return prompt

    async def _concurrent_query(self, state: InspectState) -> Dict[str, Any]:
        """Extract anomaly information for status summary"""
        tool_list = []
        param_list = []

        # First check whether machine is instance_id or nc_ip, or nc_id, and query basic info, and query operational events
        if state.machine_id_type == MachineIdType.INSTANCE_ID:
            tool_list.append("getVmBasicInfo")
            param_list.append({"instanceIds": [state.machine_id]})

            tool_list.append("listReportedOperationalEvents")
            param_list.append({"instanceId": state.machine_id, "startTime": state.start_time, "endTime": state.end_time})

        elif state.machine_id_type == MachineIdType.NC_IP:
            tool_list.append("getNcBasicInfo")
            param_list.append({"ncs": [state.machine_id]})
        elif state.machine_id_type == MachineIdType.NC_ID:
            tool_list.append("getNcBasicInfo")
            param_list.append({"ncs": [state.machine_id]})
        else:
            # Invalid machine id
            logger.warning(f"Invalid machine id: {state.machine_id}")
            return {}
        # Round to the nearest 15 minutes
        import math

        def round_to_nearest_15min(datetime_str: str, datetime_format: str = "%Y-%m-%d %H:%M:%S", round_to_seconds: int = 1) -> int:
            # ts is a timestamp (in seconds) and must be of type int or float.
            try:
                datetime_obj = datetime.strptime(datetime_str, datetime_format)
                ts = int(datetime_obj.timestamp())
                return int(math.floor(ts / round_to_seconds) * round_to_seconds)

            except Exception:
                raise ValueError(f"Invalid timestamp: {ts}")

        key_start_time = round_to_nearest_15min(state.start_time)
        key_end_time = round_to_nearest_15min(state.end_time)

        redis_key = f"inspect_information_{state.machine_id}_{key_start_time}_{key_end_time}"

        cached_data = self.redis_client.get_cache(redis_key)

        if cached_data:
            logger.info(f"Inspect information already exists in redis: {redis_key}")
            state.info_data = json.loads(str(cached_data))
            return state.info_data

        # Then query categorized monitor exceptions
        tool_list.append("listCategorizedMonitorExceptions")
        param_list.append({"machineId": state.machine_id, "startTime": state.start_time, "endTime": state.end_time})

        # Query operation records
        tool_list.append("listOperationRecords")
        param_list.append({"machineId": state.machine_id, "startTime": state.start_time, "endTime": state.end_time})

        logger.info(f"Query tools: {tool_list}")
        logger.info(f"Query params: {param_list}")

        # Async query tools

        tasks = []
        tool_names = []
        for tool_name, param in zip(tool_list, param_list):
            if tool_name in state.tool_map:
                tool = state.tool_map[tool_name]
                tasks.append(tool.ainvoke(param))
                tool_names.append(tool_name)
            else:
                logger.warning(f"Tool {tool_name} not found in tool map")
        results = {}
        if tasks:
            start_time = time.time()
            task_results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            duration = end_time - start_time
            logger.info(f"Concurrent query tools cost: {duration:.3f} seconds")
            for name, result in zip(tool_names, task_results):
                try:
                    json_result = json.loads(str(result))
                except Exception as e:
                    logger.warning(f"Failed to parse JSON for {name}: {e}, result: {result}")
                    json_result = []
                results[name] = json_result

        # Save to redis
        self.redis_client.set_cache(redis_key, json.dumps(results), ttl_seconds=60 * 60 * 24)

        state.info_data = results
        return state.info_data

    async def _organize_and_return_results(self, state: InspectState) -> CloudbotAgentChatMessage:
        """Organize results and return"""
        ext_info = {"recommendations": state.recommendations}
        message = CloudbotAgentChatMessage(
            message=state.overview,
            message_type=MessageType.AI_RESPONSE,
            ext=ext_info,
            status=MessageStatus.SUCCESS.value,
        )

        return message


if __name__ == "__main__":
    agent = InspectAgent({})

    async def main():
        async for event in agent.astream("123", additional_info={"machine_id": "i-2zebte5jxlkv2jscbwjb", "start_time": "2025-07-28 00:00:00", "end_time": "2025-07-30 23:59:59"}):
            print(event.to_sse_format())

    asyncio.run(main())
