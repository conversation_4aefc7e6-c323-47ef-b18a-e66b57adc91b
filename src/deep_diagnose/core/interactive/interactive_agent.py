import asyncio
import copy
import logging
from typing import Dict, Optional, Any, List, AsyncGenerator

from deep_diagnose.core.agent import BaseGraph
from deep_diagnose.core.events.interactive_events import InteractiveAgentEvent
from deep_diagnose.core.interactive import slow_path, fast_path
from deep_diagnose.core.interactive.concluder import conclude
from deep_diagnose.core.interactive.fast_path import question_classifier_test
from deep_diagnose.core.interactive.slow_path import planner_test, param_extractor_test
from deep_diagnose.core.interactive.state import ExecutionState
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager

logger = logging.getLogger(__name__)


class InteractiveAgent(BaseGraph):

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化函数，接收一个可选的配置字典参数。

        参数:
        - config: Optional[Dict[str, Any]] - 一个可选的配置字典，用于初始化类实例。

        该函数主要负责初始化类实例，并设置MCP工具白名单，即允许使用的一组操作或功能。
        """
        # 调用父类的初始化方法，传递配置参数
        super().__init__(config)

        # MCP工具白名单，包含了一系列允许执行的操作或功能
        self.mcp_safe_tools = [
            "runLiveMigrationCheck", "runColdMigrationCheck", "listLiveMigrationRecords", "listColdMigrationRecords",
            "runOpsEventPostpone", "runOperationSubmit", "listChangeRecords", "listOperationRecords", "listActionTrail",
            "listReportedOperationalEvents", "runVmStartStopDiagnose", "runScreenShotDiagnose",
            "runPerformanceDiagnose", "runDiagnose", "listHealthStatus", "listOnDutyStaffs", "getDiskInfo",
            "listVmHostHistory", "getUserInfo", "getNcBasicInfo", "getVmBasicInfo", "listOperationRecords",
            "listMonitorExceptions", "listKnowledge"
        ]
        self.mcp_privileged_tools = [
            "listOperationRuleMatchRecords", "listVmsOnNc"
        ]

    async def astream(
            self,
            question: str,
            messages: Optional[List[Dict[str, Any]]] = None,
            **kwargs
    ) -> AsyncGenerator[InteractiveAgentEvent, None]:
        """
        异步流式处理用户问题，逐步生成响应。

        参数:
        - question: str - 用户提出的问题内容。
        - messages: Optional[List[Dict[str, Any]]] - 历史消息列表，用于上下文参考，默认为 None。
        - **kwargs: 其他可选参数，用于扩展性支持。
            - mode:
                - None: 允许输入自然语言表示的问题，返回结果，默认
                - instruction: 指令模式，允许输入特定的指令，解析为特定的问题并返回结果
            - authority:
                - dev: 可以使用全部工具，默认
                - tam: 可以使用部分低危工具。

        返回:
        - AsyncGenerator[InteractiveAgentEvent, None]: 逐步生成的响应事件流。

        该函数负责初始化执行状态，启动主流程任务，并监控执行状态以流式返回中间结果。
        """

        # 日志记录
        logger.info(f"Starting interactive agent for question: {question} with {kwargs}")

        # 初始化执行状态
        state = await self._create_state(kwargs, question)

        # 启动主流程
        test_module = kwargs.get("additional_info", {}).get("module", None)
        if test_module is not None:
            main_task = asyncio.create_task(self._test_flow(state, test_module))
        else:
            main_task = asyncio.create_task(self._main_flow(state))

        # 监控执行状态并生成输出
        try:
            async for value, finished in self._state_monitor(state):
                interactive_agent_event = InteractiveAgentEvent()
                interactive_agent_event.parse_graph_event(value, finished)
                current_event = copy.deepcopy(interactive_agent_event)
                yield current_event
        except Exception as e:
            logger.error(f"Interactive agent task failed: {e}", exc_info=True)
            main_task.cancel()
            state.finished = True

    async def _create_state(self, kwargs, question):
        """
        创建并初始化执行状态对象。

        参数:
        - kwargs: dict - 包含执行配置的参数字典，用于控制执行流程。
        - question: str - 用户提出的问题内容，将被设置到状态对象中。

        返回:
        - ExecutionState: 初始化完成的执行状态对象，包含工具映射、问题内容等信息。
        """
        # 初始化执行状态对象
        state = ExecutionState()
        state.question = question

        # 构建可用的MCP工具字典
        authority = kwargs.get("authority", "dev")
        match authority:
            case "tam":
                mcp_whitelist = self.mcp_safe_tools
            case "dev":
                mcp_whitelist = self.mcp_safe_tools + self.mcp_privileged_tools
            case _:
                raise ValueError("Invalid authority")
        state.tool_map = {tool.name: tool for tool in await MCPToolManager().get_enabled_mcp_tools() if
                          tool.name in mcp_whitelist}
        logger.info(f"{len(state.tool_map)} MCP tools are loaded by InteractiveAgent")

        # 配置控制选项
        if kwargs.get("mode", None) == "instruction":
            # 指令模式下，禁用慢速路径，并将快速路径设置为指令模式
            state.slow_path_enable = False
            state.fast_path_mode = "instruction"
        return state

    @staticmethod
    async def _state_monitor(state: ExecutionState):
        """
        监控执行状态，定期生成当前状态描述字符串。

        参数:
        - state: ExecutionState - 当前的执行状态对象。

        返回:
        - AsyncIterator[Tuple[str, bool]]: 当状态未完成时，持续输出状态描述字符串和完成标志(False)；
          完成后输出最终状态描述字符串和完成标志(True)。
        """

        # 循环检查状态是否完成
        while not state.finished:
            # 返回当前状态的显示字符串和完成标志False
            yield state.to_display_str(), False
            # 等待0.5秒后继续检查
            await asyncio.sleep(0.5)
        # 状态已完成，返回最终状态显示字符串和完成标志True
        yield state.to_display_str(), True

    @staticmethod
    async def _main_flow(state: ExecutionState) -> None:
        """
        主流程函数，依次执行慢速路径规划与执行、总结结论，并标记任务完成。

        参数:
        - state: ExecutionState - 当前的执行状态对象。
        """

        # 并行启动慢速路径和快速路径。根据状态配置决定是否启用快速路径和慢速路径任务
        fast_path_task, slow_path_task = None, None
        if state.fast_path_enable:
            # 创建快速路径的异步任务
            fast_path_task = asyncio.create_task(fast_path.plan_and_execute(state))
        if state.slow_path_enable:
            # 创建慢速路径的异步任务
            slow_path_task = asyncio.create_task(slow_path.plan_and_execute(state))

        # 等待快速路径任务完成（如果已启动）
        if state.fast_path_enable:
            await fast_path_task

        # 在满足条件时等待慢速路径任务完成（慢速路径任务存在且未命中快速路径场景）
        if slow_path_task and state.fast_path_scenario is None:
            await slow_path_task

        # 总结结论
        state.conclusion_started = True
        if state.fast_path_scenario == "Invalid":
            # 无效问题特殊处理
            state.result_content = "您的问题目前无法识别。请完善您的问题，以便我更好地提供帮助。"
        elif state.fast_path_scenario == "KnowledgeQuery":
            # 知识库问答，等红科迁移完成，就改为调用他的问答能力
            await conclude(state)
        else:
            # 默认的智能诊断路径
            await conclude(state)

        # 标记执行完成
        state.finished = True

    @staticmethod
    async def _test_flow(state: ExecutionState, module: str | None) -> None:
        state.fast_path_match_completed = True
        state.conclusion_started = True
        try:
            match module:
                case "planner":
                    state.result_content = await planner_test(state)
                case "param_extractor":
                    state.result_content = await param_extractor_test(state)
                case "question_classifier":
                    state.result_content = await question_classifier_test(state)
                case _:
                    state.result_content = ""
        except Exception as e:
            logger.error(f"Interactive agent test task failed: {e}", exc_info=True)
        state.finished = True


async def _main():
    agent = InteractiveAgent()
    question = '''
    11.114.154.145上今天上午8点部署了哪些VM
        '''
    async for value in agent.astream(question, mode=None, authority="dev"):
        print(value.result)
        # pass


if __name__ == '__main__':
    # 强制刷新MCP缓存
    # MCPCacheManager().refresh_tools()
    # 本地测试输出日志
    from logging_config import configure_logging

    configure_logging()
    # 运行主程序
    asyncio.run(_main())
