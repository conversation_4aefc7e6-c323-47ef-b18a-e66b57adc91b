from typing import Any
from pydantic import BaseModel

from deep_diagnose.common.utils.machine_utils import MachineIdType


class InspectState(BaseModel):
    # 通用
    machine_id: str = ""
    start_time: str = ""
    end_time: str = ""
    machine_id_type: MachineIdType = MachineIdType.UNKNOWN
    tool_map: dict[str, Any] = {}

    # 数据
    info_data: dict[str, Any] = {}
    # Flag
    finished: bool = False

    # 结果
    recommendations: list[dict[str, Any]] = []
    overview: str = ""
