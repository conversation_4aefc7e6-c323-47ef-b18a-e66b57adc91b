"""
协调器Agent - 负责任务分发和客户沟通
"""

from typing import Literal, Optional, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langgraph.types import Command

from .base import SubAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template
from deep_diagnose.core.reasoning.planning import create_sop_service


from deep_diagnose.common.config.core.configuration import Configuration # New import


@tool
def handoff_to_planner(
    task_title: Annotated[str, "The title of the task to be handed off."],
):
    """Handoff to planner agent to do plan."""
    return


class CoordinatorAgent(SubAgent):
    """协调器Agent"""
    
    def __init__(self, config_obj: Configuration):
        super().__init__("coordinator", "coordinator")
        # Initialize SOPService
        self.sop_service = create_sop_service()
    
    def get_default_tools(self):
        return [handoff_to_planner]
    
    async def _build_sop_capabilities(self) -> str:
        """
        构建SOP能力描述信息
        删除工具描述，增加scenario描述信息
        """
        sops = await self.sop_service.get_all_sops()
        lines = []

        if sops:
            lines.append("\n")
            lines.append("### 典型问题类型与处理能力")

            for sop in sops:
                lines.append(f"#### {sop.name}")

                # 添加scenario描述信息
                if sop.scenario:
                    # 处理多行scenario内容，只取第一部分作为简要说明
                    scenario_lines = sop.scenario.strip().split('\n')
                    scenario_brief = scenario_lines[0].replace('适用场景：', '').strip()
                    lines.append(f"  **适用场景：** {scenario_brief}")

                # 添加示例问题
                if sop.example_user_queries:
                    lines.append("  **示例：**")
                    for ex in sop.example_user_queries[:3]:
                        lines.append(f"    - {ex}")
                lines.append("\n")

        return "\n".join(lines)
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command:
        # Create Configuration object from RunnableConfig
        configurable = Configuration.from_runnable_config(config)
        
        # Use independent function to build capabilities description
        state["capabilities"] = await self._build_sop_capabilities()
        
        messages = apply_prompt_template("agent_coordinator", state)
        response = await get_llm_by_type(AGENT_LLM_MAP["coordinator"]).bind_tools([handoff_to_planner]).ainvoke(messages)
        
        if not response.tool_calls:
            self.logger.warning("No tool calls found, terminating workflow")
            return Command(goto="__end__")
        
        # Determine next step
        goto = "background_investigator" if state.get("enable_background_investigation") else "planner"
        
        return Command(goto=goto)


async def coordinator_node(state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command[Literal["planner", "background_investigator", "__end__"]]:
    # Instantiate CoordinatorAgent within the node function
    configurable = Configuration.from_runnable_config(config)
    _coordinator = CoordinatorAgent(configurable)
    return await _coordinator.execute(state, config)
