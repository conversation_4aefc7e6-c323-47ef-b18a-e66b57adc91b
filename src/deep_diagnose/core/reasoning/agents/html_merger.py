"""
HTML报告合并器 - 简化版本

简化原则：
1. 合并相似功能，减少类的数量
2. 简化HTML模板，使用更简洁的生成方式
3. 统一上传逻辑，避免重复代码
4. 保持核心功能，移除过度抽象
"""

import tempfile
import os
import asyncio
import logging
import json
from datetime import datetime
from typing import Optional, Dict, Any
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command
from langgraph.graph import END

from .base import SubAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template
from deep_diagnose.storage.redis_client import RedisClient
from deep_diagnose.storage.oss_client import OssClient

logger = logging.getLogger(__name__)


class HtmlUtils:
    """HTML工具类 - 合并清理和模板功能"""
    
    @staticmethod
    def clean_content(content: str) -> str:
        """清理HTML内容"""
        if not content or not content.strip():
            return "<div>内容缺失</div>"
        
        content = content.strip()
        # 移除代码块标记
        if content.startswith(('```html', '```')):
            content = content[7:] if content.startswith('```html') else content[3:]
        if content.endswith('```'):
            content = content[:-3]
        
        return content.strip()
    
    @staticmethod
    def validate_html(html_content: str) -> bool:
        """验证HTML质量"""
        if not html_content or len(html_content.strip()) < 100:
            return False
        required_tags = ["<html", "<head", "<body"]
        return all(tag in html_content.lower() for tag in required_tags)
    
    @staticmethod
    def generate_report(sections: Dict[str, str], title: str = "ECS诊断报告") -> str:
        """生成HTML报告"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 简化的HTML模板
        html_sections = []
        section_titles = {
            "problem_description_html": "问题概述",
            "diagnosis_info_html": "诊断分析", 
            "key_findings_html": "关键发现",
            "evidence_chain_html": "支撑证据",
            "summary_conclusion_html": "总结建议"
        }
        
        for key, section_title in section_titles.items():
            content = HtmlUtils.clean_content(sections.get(key, ""))
            html_sections.append(f"""
            <div class="section">
                <h2>{section_title}</h2>
                {content}
            </div>""")
        
        return f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {{ background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%); color: #e2e8f0; }}
        .section {{ border-top: 2px solid #3b82f6; margin: 2rem 0; padding-top: 1rem; }}
        h1 {{ color: #3b82f6; }}
        h2 {{ color: #60a5fa; }}
    </style>
</head>
<body class="min-h-screen p-6">
    <div class="max-w-5xl mx-auto">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold mb-2">ECS 深度诊断报告</h1>
            <p class="text-gray-400">生成时间: {timestamp}</p>
        </div>
        {''.join(html_sections)}
    </div>
</body>
</html>"""




class HtmlReportMergerAgent(SubAgent):
    """HTML报告合并代理 - 简化版本"""
    
    def __init__(self):
        super().__init__("html_report_merger", "html_report_merger")
        self.oss_timeout = 30.0
        self.redis_timeout = 10.0
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command:
        """执行HTML报告合并"""
        try:
            # 1. 提取HTML节
            sections = self._extract_sections(state)
            user_query = self._extract_user_query(state)
            
            # 2. 尝试LLM合并，失败则使用规则合并
            complete_html = await self._merge_html(sections, state, user_query)
            
            # 3. 验证并回退
            if not HtmlUtils.validate_html(complete_html):
                logger.warning("HTML质量验证失败，使用简化报告")
                complete_html = HtmlUtils.generate_report(sections, user_query)
            
            # 4. 上传和存储
            request_id = state.get("request_id", "unknown")
            api_url = await self._upload_report(complete_html, request_id)
            
            # 5. 创建报告状态
            report_state = self._create_report_state(complete_html, state, api_url)
            
            # 6. 更新状态
            reports = state.get("reports", [])
            reports.append(report_state)
            
            logger.info(f"HTML报告合并完成 - 内容长度: {len(complete_html)}")
            
            # 简洁方案：直接将URL添加到state，确保传播到reasoning_event
            update_data = {
                "reports": reports,
                "merged_html_report": complete_html
            }
            
            # 如果有API URL，直接添加到state中，供reasoning_event使用
            if api_url:
                update_data["report_urls"] = [api_url]
                logger.info(f"✅ 报告URL已添加到state: {api_url}")
            
            return Command(update=update_data, goto=END)
            
        except Exception as e:
            logger.error(f"HTML报告合并失败: {e}", exc_info=True)
            return Command(update={}, goto=END)
    
    def _extract_sections(self, state: ReasoningState) -> Dict[str, str]:
        """提取HTML节"""
        section_keys = [
            "problem_description_html", "diagnosis_info_html", 
            "key_findings_html", "evidence_chain_html", "summary_conclusion_html"
        ]
        return {key: state.get(key, "") for key in section_keys}
    
    def _extract_user_query(self, state: ReasoningState) -> str:
        """提取用户查询"""
        messages = state.get("messages", [])
        for msg in reversed(messages):
            if hasattr(msg, "content") and msg.content:
                return msg.content
            elif isinstance(msg, str):
                return msg
        return ""
    
    async def _merge_html(self, sections: Dict[str, str], state: ReasoningState, user_query: str) -> str:
        """合并HTML - 智能选择策略，优先使用规则合并"""
        # 判断内容复杂度
        total_length = sum(len(content) for content in sections.values())
        
        # 提高阈值，更多情况下使用规则合并（更稳定）
        if total_length < 500:  # 从100提高到500
            logger.info(f"内容长度 {total_length} < 500，使用规则合并")
            return HtmlUtils.generate_report(sections, user_query)
        
        # 检查是否有复杂的HTML结构需要LLM处理
        has_complex_html = any(
            '<table' in content.lower() or 
            '<ul' in content.lower() or 
            '<ol' in content.lower() or
            len(content) > 1000
            for content in sections.values()
        )
        
        if not has_complex_html:
            logger.info("内容结构简单，使用规则合并")
            return HtmlUtils.generate_report(sections, user_query)
        
        # 尝试LLM合并（仅在内容复杂时）
        try:
            logger.info(f"内容复杂（长度: {total_length}），尝试LLM智能合并")
            return await self._llm_merge(sections, state, user_query)
        except Exception as e:
            logger.warning(f"LLM合并失败，回退到规则合并: {type(e).__name__}: {e}")
            return HtmlUtils.generate_report(sections, user_query)
    
    async def _llm_merge(self, sections: Dict[str, str], state: ReasoningState, user_query: str) -> str:
        """LLM智能合并 - 增强错误处理和重试机制"""
        # 构建上下文
        context = {
            "user_query": user_query,
            "final_report": state.get("final_report", ""),
            "observations": state.get("observations", []),
            "current_plan": state.get("current_plan", ""),
            "messages": []
        }
        
        # 清理HTML片段
        for key, content in sections.items():
            context[key] = HtmlUtils.clean_content(content)
        
        # 调用LLM - 增加重试机制
        messages = apply_prompt_template("agent_html_report_merger", context)
        llm = get_llm_by_type(AGENT_LLM_MAP.get("html_merger", "basic"))
        
        # 多次重试，逐步降低超时时间
        retry_timeouts = [120.0, 90.0, 60.0]  # 2分钟 -> 1.5分钟 -> 1分钟
        last_error = None
        
        for attempt, timeout in enumerate(retry_timeouts, 1):
            try:
                logger.info(f"LLM调用尝试 {attempt}/{len(retry_timeouts)}，超时: {timeout}秒")
                
                # 配置LLM重试参数
                llm_with_config = llm.with_config({
                    "max_retries": 2,
                    "retry_delay": 1.0
                })
                
                response = await asyncio.wait_for(
                    llm_with_config.ainvoke(messages),
                    timeout=timeout
                )
                
                merged_html = getattr(response, "content", "")
                if not merged_html or not merged_html.strip():
                    raise ValueError("LLM返回空响应")
                
                # 清理LLM输出
                content = HtmlUtils.clean_content(merged_html)
                if not content.startswith("<!DOCTYPE") and content.startswith("<html"):
                    content = "<!DOCTYPE html>\n" + content
                
                logger.info(f"LLM调用成功，尝试次数: {attempt}")
                return content
                
            except asyncio.TimeoutError as e:
                last_error = e
                logger.warning(f"LLM调用超时 (尝试 {attempt}/{len(retry_timeouts)})，超时时间: {timeout}秒")
                if attempt < len(retry_timeouts):
                    await asyncio.sleep(2)  # 重试前等待2秒
                continue
                
            except asyncio.CancelledError as e:
                last_error = e
                logger.warning(f"LLM调用被取消 (尝试 {attempt}/{len(retry_timeouts)})")
                if attempt < len(retry_timeouts):
                    await asyncio.sleep(2)
                continue
                
            except Exception as e:
                last_error = e
                logger.error(f"LLM调用失败 (尝试 {attempt}/{len(retry_timeouts)}): {e}")
                if attempt < len(retry_timeouts):
                    await asyncio.sleep(2)
                continue
        
        # 所有重试都失败了
        logger.error(f"LLM调用所有重试都失败，最后错误: {last_error}")
        raise last_error or Exception("LLM调用失败")
    
    async def _upload_report(self, html_content: str, request_id: str) -> Optional[str]:
        """上传报告到OSS并返回API URL"""
        try:
            # 上传到OSS
            oss_success = await asyncio.wait_for(
                asyncio.get_event_loop().run_in_executor(
                    None, self._upload_to_oss_sync, html_content, request_id
                ),
                timeout=self.oss_timeout
            )
            
            if oss_success:
                # 返回API URL而不是OSS签名URL
                api_url = f"https://ecs-deep-diagnose.aliyun-inc.com/api/v1/reporter/{request_id}"
                
                # 异步存储到Redis（重要：为后续处理器提供URL）
                asyncio.create_task(self._store_to_redis(api_url, request_id))
                logger.info(f"报告上传完成，请求ID: {request_id}")
                return api_url
            
            return None
            
        except Exception as e:
            logger.error(f"报告上传失败: {e}", exc_info=True)
            return None
    
    def _upload_to_oss_sync(self, html_content: str, request_id: str) -> bool:
        """同步上传到OSS"""
        try:
            oss_client = OssClient()
            
            with tempfile.NamedTemporaryFile(
                mode="w", suffix=".html", delete=False, encoding="utf-8"
            ) as temp_file:
                temp_file.write(html_content)
                html_path = temp_file.name
            
            try:
                # 使用简化的命名规则：diagnosis-reports/{request_id}.html
                oss_key = f"diagnosis-reports/{request_id}.html"
                oss_client.upload_file(html_path, oss_key)
                return True
            finally:
                if os.path.exists(html_path):
                    os.unlink(html_path)
                    
        except Exception as e:
            logger.error(f"OSS上传失败: {e}", exc_info=True)
            return False
    
    async def _store_to_redis(self, api_url: str, request_id: str):
        """存储API URL到Redis，供后续处理器使用"""
        try:
            redis_client = RedisClient()
            redis_key = f"diagnosis:report_url:{request_id}"
            
            await asyncio.wait_for(
                redis_client.set_cache_async(redis_key, api_url, ttl_seconds=30*24*3600),
                timeout=self.redis_timeout
            )
            
            # 发布消息通知报告就绪
            channel = f"diagnosis:report_ready:{request_id}"
            message = json.dumps({
                "request_id": request_id,
                "url": api_url,
                "timestamp": datetime.now().isoformat(),
                "type": "report_ready"
            }, ensure_ascii=False)
            
            await redis_client.publish_async(channel, message)
            logger.info(f"报告URL已存储到Redis: {request_id} -> {api_url}")
            
        except Exception as e:
            logger.warning(f"Redis存储失败，请求ID: {request_id}: {e}")
    
    def _create_report_state(self, html_content: str, state: ReasoningState, api_url: Optional[str]):
        """创建报告状态"""
        from deep_diagnose.core.reasoning.workflow.types import ReportState
        
        reports = state.get("reports", [])
        current_iteration = (reports[-1].iteration if reports else 0) + 1
        
        report_state = ReportState(
            report_html=html_content,
            generator="smart_merge",
            iteration=current_iteration,
            validate_status="passed",
            reflection_status="passed"
        )
        
        if api_url:
            report_state.url = api_url
            
        return report_state


# 单例和节点函数
_html_report_merger = HtmlReportMergerAgent()

async def html_report_merger_node(state: ReasoningState, config: Optional[RunnableConfig] = None) -> Dict[str, Any]:
    """LangGraph 节点：执行HTML报告合并"""
    return await _html_report_merger.execute(state, config)
