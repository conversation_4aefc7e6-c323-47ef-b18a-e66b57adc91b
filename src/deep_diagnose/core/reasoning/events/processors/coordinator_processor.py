"""
计划器消息处理器

处理计划器Agent的消息，重命名自 PlannerEventProcessor
"""

import logging
from typing import Dict, Any, List
import time
from deep_diagnose.core.events.processors.base_processor import BaseMessageProcessor
from deep_diagnose.core.events.models import AgentMessage

logger = logging.getLogger(__name__)


class CoordinatorMessageProcessor(BaseMessageProcessor):
    """计划器消息处理器"""

    def __init__(self, request_id: str = None):
        """
        初始化计划器消息处理器

        Args:
            request_id: 请求ID，用于标识处理上下文
        """
        super().__init__(request_id=request_id)

    def get_agent_name(self) -> str:
        return "coordinator"

    def process_messages(self, messages: List[AgentMessage]) -> Dict[str, Any]:
        try:
            # Reporter 主要负责生成最终报告内容和URLs
            result_content = ""
            has_tool_calls = False
            has_finished_message = False
            
            # A. 去抖：仅当合并间隔超过阈值时才进行合并
            # 只处理已完成的消息，并过滤掉 silent LLM 调用
            for message in messages:
                # 检查消息是否已完成 - 修复：使用正确的字段名 is_finished
                if message.is_finished:
                    has_finished_message = True
                    
                # 过滤掉 silent LLM 调用的消息
                if message.content.strip():
                    result_content += message.content
                    
                # 检查是否有工具调用
                if message.tool_executions:
                    has_tool_calls = True
                    for tool_execution in message.tool_executions:
                        if tool_execution.call_name=='handoff_to_planner':
                            result_content = "正在协调各智能体，制定诊断计划"

            result = {}
            if result_content.strip():
                result["understanding"] = result_content.strip()
            
            # 修复：Coordinator不应该设置finished状态，这会导致整个推理流程提前结束
            # Coordinator只是协调角色，finished状态应由Reporter或最终的URL设置来决定
            if has_finished_message and not has_tool_calls and result_content.strip():
                result["finished"] = True
                logger.info(f"Coordinator message finished without tool calls, setting finished=True")

            return result
        except Exception as e:
            logger.error(f"Error processing reporter messages: {e}", exc_info=True)
            return {}