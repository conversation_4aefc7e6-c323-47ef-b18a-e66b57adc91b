"""
报告生成器消息处理器

处理报告生成器Agent的消息，重命名自 ReporterEventProcessor
"""

import logging
import time
from typing import Dict, Any, List

from deep_diagnose.core.events.processors.base_processor import BaseMessageProcessor
from deep_diagnose.core.events.models import AgentMessage

logger = logging.getLogger(__name__)


class ReporterMessageProcessor(BaseMessageProcessor):
    # A. 简单去抖：仅当间隔超过阈值时才合并（避免高频拼接），默认200ms
    MERGE_DEBOUNCE_MS = 200
    _last_merge_ts = 0.0
    """报告生成器消息处理器"""

    def __init__(self, request_id: str = None):
        """
        初始化报告生成器消息处理器
        
        Args:
            request_id: 请求ID，用于标识处理上下文
        """
        super().__init__(request_id=request_id)

    def get_agent_name(self) -> str:
        return "reporter"

    def process_messages(self, messages: List[AgentMessage]) -> Dict[str, Any]:
        """处理报告生成器消息"""
        try:
            # Reporter 主要负责生成最终报告内容和URLs
            result_content = ""
            # A. 去抖：仅当合并间隔超过阈值时才进行合并
            now = time.time() * 1000
            if (now - self._last_merge_ts) >= self.MERGE_DEBOUNCE_MS:
                # 只处理已完成的消息，并过滤掉 silent LLM 调用
                for message in messages:
                    # 过滤掉 silent LLM 调用的消息
                    if self._is_silent_node_message(message.tags):
                        continue
                    if message.content:
                        result_content += message.content
                self._last_merge_ts = now
                    
            result = {}
            if result_content.strip():
                result["result"] = result_content.strip()
            return result
        except Exception as e:
            logger.error(f"Error processing reporter messages: {e}", exc_info=True)
            return {}

    def _is_silent_node_message(self, tags: list[str]) -> bool:
        """检查是否是 silent 节点的消息（应该忽略）"""
        # 如果内容主要是这些关键词组成，且没有结构化的计划，可能是 silent 节点
        if "silent_llm_call" in tags:
            return True

        return False