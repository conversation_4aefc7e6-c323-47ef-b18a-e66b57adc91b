"""
Agent  事件模型 - 简化架构

MessageCollector -> MessageRepository -> MessageProcessor
"""

import logging
import asyncio
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Set

from deep_diagnose.common.constants.app import REQUEST_ID_FIELD
from deep_diagnose.core.events.base_event import BaseAgentOutputEvent
from deep_diagnose.core.events.collectors import MessageCollector
# 导入简化的架构组件
from deep_diagnose.core.events.storage import MessageRepository
from deep_diagnose.core.reasoning.events.reasoning_event_processor import ReasoningEventMessageProcessor

# 模块级别的logger实例
logger = logging.getLogger(__name__)


@dataclass
@BaseAgentOutputEvent.register_event_type("reasoning_event")
class ReasoningAgentEvent(BaseAgentOutputEvent):
    """推理智能体事件V5 - 简化的消息存储架构"""

    # 业务数据字段（保持兼容）
    understanding:str=""
    thought: str = ""
    plan_steps: List[Dict[str, Any]] = field(default_factory=list)
    executions: List[Dict[str, Any]] = field(default_factory=list)
    result: str = ""
    urls: List[Dict[str, Any]] = field(default_factory=list)
    # 请求ID字段 (替代thread_id)
    request_id: Optional[str] = None
    # 内部版本号：每次应用事件数据时递增（不会序列化到 SSE）
    _version: int = 0
    finished: bool = False

    def __post_init__(self):
        """初始化简化的架构组件"""
        # 构建MessageRepository对象
        self.message_repository = MessageRepository()

        # 消息收集器
        self.collector = MessageCollector()

        # 业务消息处理器
        self.business_processor = ReasoningEventMessageProcessor(request_id=self.request_id)

        # C. 解耦收集与处理：使用队列串行处理，避免阻塞推送链路
        self._queue: asyncio.Queue = asyncio.Queue(maxsize=1000)
        self._processor_task: Optional[asyncio.Task] = None
        self._stop_event = asyncio.Event()
        # A. 去抖：限制 Reporter 合并频率（后续在 reporter 内实现，但这里留时间戳供扩展）
        self._last_process_ts: float = 0.0
        # 版本变更通知事件：当 _version 递增时触发，用于上层等待异步处理完成
        self._version_changed = asyncio.Event()

    def parse(self, raw_event: str) -> bool:
        """解析原始 SSE 事件（保留以满足基类要求）"""
        return False

    async def _processor_loop(self):
        """后台处理协程：串行消费队列，做增量业务处理"""
        while not self._stop_event.is_set():
            try:
                item = await self._queue.get()
                if item is None:
                    continue
                message_chunk, message_metadata = item
                change = self.collector.collect_message(self.message_repository, message_chunk, message_metadata)
                if not change:
                    continue
                # 增量处理：总是附带处理 reporter，确保最终 result 能及时汇总
                agents = []
                if change.agent_name:
                    agents.append(change.agent_name)
                event_data = self.business_processor.process_business_messages(self.message_repository, only_agents=agents)
                if not event_data:
                    continue
                # 应用更新
                self._apply_event_data(event_data)
            except Exception as e:
                logger.error(f"Processor loop error: {e}", exc_info=True)

    def handle_message(self, message_chunk, message_metadata: dict) -> bool:
        """
        处理单个消息（异步-only）：
        - 仅将消息入队，由后台协程串行处理 Collector 和 BusinessProcessor
        - 避免同步与异步各处理一次导致的重复合并/双重开销
        """
        try:
            # 确保后台处理协程已运行
            if self._processor_task is None or self._processor_task.done():
                loop = asyncio.get_event_loop()
                self._processor_task = loop.create_task(self._processor_loop())

            # 仅入队，交由后台处理
            try:
                self._queue.put_nowait((message_chunk, message_metadata))
            except asyncio.QueueFull:
                logger.warning("Processor queue is full; dropping message to avoid blocking")
                return False

            return True

        except Exception as e:
            logger.error(f"Error handling message: {e}", exc_info=True)
            return False

    def stop(self):
        """停止后台处理协程"""
        try:
            self._stop_event.set()
            if self._processor_task and not self._processor_task.done():
                self.finished = True
                self._queue.put_nowait(None)
        except Exception:
            pass

    def _apply_event_data(self, event_data: Dict[str, Any]):
        """应用事件数据到业务字段，并标记版本递增（触发流式输出）"""
        for key, value in event_data.items():
            setattr(self, key, value)
            
        # 简洁方案：如果state包含report_urls，转换为标准的urls格式
        if "report_urls" in event_data and event_data["report_urls"]:
            for url in event_data["report_urls"]:
                url_info = {
                    "url": url,
                    "name": "CloudBot智能体-长推理诊断报告",
                    "type": "report"
                }
                if url_info not in self.urls:
                    self.urls.append(url_info)
                    logger.info(f"✅ URL已添加到reasoning_event: {url}")
                    
        # 避免微小变动时丢失一次刷新机会，统一递增版本
        self._version += 1
        if self.urls and len(self.urls) > 0 and self.urls[0].get("url"):
            self.finished = True
        # 通知版本变更（唤醒等待者）
        try:
            self._version_changed.set()
        except Exception:
            pass

    def parse_graph_event(self, agent_path: tuple, event_data: tuple, request_id: str) -> bool:
        """
        解析LangGraph事件

        Args:
            agent_path: Agent路径元组
            event_data: 事件数据元组 (message_chunk, message_metadata)
            request_id: 请求ID

        Returns:
            bool: 是否有业务数据更新
        """
        if len(event_data) != 2:
            return False

        message_chunk, message_metadata = event_data

        # 添加请求ID到元数据
        if isinstance(message_metadata, dict):
            message_metadata = message_metadata.copy()
            message_metadata[REQUEST_ID_FIELD] = request_id

        return self.handle_message(message_chunk, message_metadata)

    def to_sse_format(self) -> str:
        """转换为SSE格式（保持兼容）"""
        data = {
            "understanding":self.understanding,
            "thought": self.thought,
            "plan_steps": self.plan_steps,
            "executions": self.executions,
            "result": self.result,
            "urls": self.urls,
            "request_id": self.request_id,
            "event_type": self.event_type,  # 重要：添加event_type字段
            "finished":self.finished
        }

        import json
        return json.dumps(data, ensure_ascii=False)

    @classmethod
    def from_sse_format(cls, event_data: str) -> Optional['ReasoningAgentEvent']:
        """从 SSE 格式反序列化推理事件"""
        try:
            import json
            data = json.loads(event_data)
            
            # 检查event_type是否匹配
            if data.get("event_type") and data.get("event_type") != "reasoning_event":
                logger.warning(f"Event type mismatch: expected 'reasoning_event', got '{data.get('event_type')}'")
                return None
            
            # 创建事件实例，传入request_id
            request_id = data.get("request_id") or data.get("thread_id")  # 向后兼容
            event = cls(request_id=request_id)

            # 填充各个字段
            event.understanding = data.get("understanding", "")

            event.thought = data.get("thought", "")
            event.plan_steps = data.get("plan_steps", [])
            event.executions = data.get("executions", [])
            event.result = data.get("result", "")
            event.urls = data.get("urls", [])
            event.finished = data.get("finished", False)

            logger.debug(f"Successfully deserialized ReasoningAgentEvent with request_id={request_id}, {len(event.plan_steps)} plan steps, {len(event.executions)} executions")
            return event
        except Exception as e:
            logger.error(f"Failed to deserialize ReasoningAgentEvent: {e}")
            return None

    def _is_final_event(self, event_data: str) -> bool:
        """
        判断是否为最终事件（基类默认实现）

        Args:
            event_data: SSE 格式的事件数据字符串

        Returns:
            bool: 是否为最终事件
        """
        return self.finished
