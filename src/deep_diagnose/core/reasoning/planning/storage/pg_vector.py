import logging
import hashlib
import yaml
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass

try:
    from langchain_postgres import PGVector
    POSTGRES_AVAILABLE = True
except ImportError as e:
    POSTGRES_AVAILABLE = False
    POSTGRES_IMPORT_ERROR = str(e)
    # Create a dummy PGVector class for type hints
    class PGVector:
        pass

from langchain_core.embeddings import Embeddings
from langchain_core.documents import Document

try:
    from deep_diagnose.core.reasoning.planning.dashscope_embedding import DashScopeEmbeddings
    DASHSCOPE_AVAILABLE = True
except ImportError:
    DASHSCOPE_AVAILABLE = False
    DashScopeEmbeddings = None

from deep_diagnose.core.reasoning.planning.models import SOP
from deep_diagnose.common.config import get_config
from .base import SOPStore

logger = logging.getLogger(__name__)

# Create a function to get DashScope embeddings
def get_dashscope_embeddings():
    """Get DashScope embeddings using local custom implementation"""
    if not DASHSCOPE_AVAILABLE:
        raise ImportError("DashScope embeddings not available. Please check the dashscope_embedding.py file")
    
    try:
        # Use the local custom DashScope embeddings class
        # This class automatically gets API key from config and has custom embed methods
        embeddings = DashScopeEmbeddings(model="text-embedding-v4")
        logger.info("✅ Created custom DashScope embeddings from local implementation")
        return embeddings
    except Exception as e:
        logger.error(f"Failed to create custom DashScope embeddings: {e}")
        raise

# Initialize global EMBEDDINGS using the same function
try:
    EMBEDDINGS = get_dashscope_embeddings()
    logger.info("✅ Global EMBEDDINGS initialized with DashScope")
except Exception as e:
    logger.warning(f"Failed to initialize global EMBEDDINGS: {e}")
    EMBEDDINGS = None

@dataclass
class SOPDocument:
    """Enhanced SOP document with full content and metadata"""
    sop_id: str
    name: str
    scenario: str
    template_file_path: str
    content: str
    example_user_queries: List[str]
    tool_dependencies: List[str]
    category: str
    subcategory: str
    file_path: str
    created_at: datetime
    updated_at: datetime
    content_hash: str


class SOPLoader:
    """Loads SOP documents from configuration and markdown files"""
    
    def __init__(self, base_path: str = "src/deep_diagnose/prompts/sop"):
        self.base_path = Path(base_path)
        self.config_path = self.base_path / "configs" / "sop_diagnosis_config.yaml"
    
    def load_config(self) -> Dict[str, Any]:
        """Load SOP configuration from YAML file"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load SOP config from {self.config_path}: {e}")
            return {}
    
    def load_markdown_content(self, file_path: str) -> str:
        """Load markdown content from file"""
        full_path = self.base_path / file_path
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Failed to load markdown content from {full_path}: {e}")
            return ""
    
    def parse_file_path(self, file_path: str) -> Tuple[str, str]:
        """Parse category and subcategory from file path"""
        parts = Path(file_path).parts
        if len(parts) >= 2:
            category = parts[0]  # customer/instance/nc
            subcategory = parts[1]  # diagnosis/reporting
            return category, subcategory
        return "unknown", "unknown"
    
    def calculate_content_hash(self, content: str) -> str:
        """Calculate hash for content change detection"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def load_all_sops(self) -> List[SOPDocument]:
        """Load all SOP documents from config and markdown files"""
        config = self.load_config()
        sop_documents = []
        
        diagnosis_sops = config.get('diagnosis_sops', [])
        
        for sop_config in diagnosis_sops:
            try:
                # Load markdown content
                template_file_path = sop_config.get('template_file_path', '')
                content = self.load_markdown_content(template_file_path)
                
                if not content:
                    logger.warning(f"No content found for SOP {sop_config.get('sop_id')}")
                    continue
                
                # Parse category and subcategory
                category, subcategory = self.parse_file_path(template_file_path)
                
                # Create SOP document
                now = datetime.now()
                sop_doc = SOPDocument(
                    sop_id=sop_config.get('sop_id', ''),
                    name=sop_config.get('name', ''),
                    scenario=sop_config.get('scenario', ''),
                    template_file_path=template_file_path,
                    content=content,
                    example_user_queries=sop_config.get('example_user_queries', []),
                    tool_dependencies=sop_config.get('tool_dependencies', []),
                    category=category,
                    subcategory=subcategory,
                    file_path=str(self.base_path / template_file_path),
                    created_at=now,
                    updated_at=now,
                    content_hash=self.calculate_content_hash(content)
                )
                
                sop_documents.append(sop_doc)
                logger.info(f"Loaded SOP: {sop_doc.sop_id} ({sop_doc.name})")
                
            except Exception as e:
                logger.error(f"Failed to load SOP {sop_config.get('sop_id', 'unknown')}: {e}")
                continue
        
        logger.info(f"Successfully loaded {len(sop_documents)} SOP documents")
        return sop_documents


class PGVectorSOPStore(SOPStore):
    """SOP store implementation using PGVector with LangChain."""

    def __init__(
        self, 
        connection_string: Optional[str] = None, 
        collection_name: str = "sops",
        embeddings: Optional[Embeddings] = None,
        base_path: str = "src/deep_diagnose/prompts/sop"
    ):
        # Check if PostgreSQL dependencies are available
        if not POSTGRES_AVAILABLE:
            error_msg = f"PostgreSQL dependencies not available: {POSTGRES_IMPORT_ERROR}"
            logger.error(error_msg)
            logger.error("To fix this issue:")
            logger.error("1. On macOS: brew install postgresql libpq")
            logger.error("2. Install Python packages: pip install 'psycopg[binary]' 'psycopg2-binary' langchain-postgres")
            logger.error("3. Set environment variables if needed:")
            logger.error("   export PATH=\"/opt/homebrew/opt/libpq/bin:$PATH\"")
            raise ImportError(error_msg)
        
        # Get PostgreSQL configuration from config system
        if connection_string is None:
            config = get_config()
            postgres_config = config.infrastructure.postgres
            
            # URL encode password to handle special characters like @, :, etc.
            from urllib.parse import quote_plus
            
            encoded_user = quote_plus(str(postgres_config.user))
            encoded_password = quote_plus(str(postgres_config.password))
            encoded_host = quote_plus(str(postgres_config.host))
            encoded_database = quote_plus(str(postgres_config.database))
            
            # Create connection strings for different purposes
            # For psycopg (direct connection testing)
            psycopg_connection_string = (
                f"postgresql://{encoded_user}:{encoded_password}"
                f"@{encoded_host}:{postgres_config.port}/{encoded_database}"
            )
            
            # For SQLAlchemy/LangChain (PGVector)
            connection_string = (
                f"postgresql+psycopg://{encoded_user}:{encoded_password}"
                f"@{encoded_host}:{postgres_config.port}/{encoded_database}"
            )
            
            # Log connection details (without password)
            logger.info(f"PostgreSQL connection details:")
            logger.info(f"  Host: {postgres_config.host}")
            logger.info(f"  Port: {postgres_config.port}")
            logger.info(f"  Database: {postgres_config.database}")
            logger.info(f"  User: {postgres_config.user}")
            logger.info(f"  Password length: {len(str(postgres_config.password))} chars")
            logger.info(f"  Password contains '@': {'@' in str(postgres_config.password)}")
            logger.info(f"  Password contains ':': {':' in str(postgres_config.password)}")
            logger.info(f"  Connection string (masked): postgresql+psycopg://{encoded_user}:***@{encoded_host}:{postgres_config.port}/{encoded_database}")
            
            # Additional debugging for connection issues
            if not postgres_config.host or postgres_config.host == 'localhost':
                logger.warning("Using localhost - make sure PostgreSQL is running locally")
            if not str(postgres_config.port).isdigit():
                logger.error(f"Invalid port: {postgres_config.port}")
            if not postgres_config.database:
                logger.error("Database name is empty")
        
        self.connection_string = connection_string
        self.collection_name = collection_name
        
        # Initialize embeddings with proper configuration
        if embeddings is None:
            # Create DashScope embeddings
            try:
                self.embeddings = get_dashscope_embeddings()
                logger.info(f"Using DashScope embeddings: {type(self.embeddings).__name__}")
            except Exception as e:
                logger.error(f"Failed to create DashScope embeddings: {e}")
                raise
        else:
            self.embeddings = embeddings
            logger.info(f"Using provided embeddings: {type(self.embeddings).__name__}")
            
        self.loader = SOPLoader(base_path)
        
        # Test connection before initializing PGVector
        # Use psycopg-compatible connection string for testing
        test_connection_string = psycopg_connection_string if connection_string is None else connection_string.replace("postgresql+psycopg://", "postgresql://")
        self._test_connection(test_connection_string)
        
        try:
            logger.info(f"Attempting to initialize PGVector store...")
            logger.info(f"Collection name: {collection_name}")
            logger.info(f"Using embeddings: {type(self.embeddings).__name__}")
            
            # Initialize PGVector store with async support
            from sqlalchemy.ext.asyncio import create_async_engine
            
            # Create async engine first
            async_engine = create_async_engine(
                connection_string,
                echo=False,
                future=True
            )
            
            self.vector_store = PGVector(
                embeddings=self.embeddings,
                collection_name=collection_name,
                connection=connection_string,
                use_jsonb=True,
                async_mode=True,  # Enable async mode
            )
            
            # Set the async engine manually
            self.vector_store._async_engine = async_engine
            
            # Mark that we need to initialize the async engine on first use
            self._async_initialized = False
            
            logger.info(f"✅ Successfully initialized PGVectorSOPStore with collection: {collection_name}")
        except Exception as e:
            logger.error(f"❌ Failed to initialize PGVector store: {e}")
            logger.error(f"Exception type: {type(e).__name__}")
            
            # Specific error handling
            if "nodename nor servname provided" in str(e):
                logger.error("🔍 DNS/Host resolution error detected:")
                logger.error("  - Check if the host name is correct")
                logger.error("  - Verify network connectivity")
                logger.error("  - Try using IP address instead of hostname")
            elif "password" in str(e).lower():
                logger.error("🔍 Password-related error detected:")
                logger.error("  - Check if password contains special characters (@, :, etc.)")
                logger.error("  - Verify password encoding")
            elif "connection refused" in str(e).lower():
                logger.error("🔍 Connection refused error:")
                logger.error("  - Make sure PostgreSQL is running")
                logger.error("  - Check if the port is correct")
                logger.error("  - Verify firewall settings")
            
            logger.error("💡 General troubleshooting:")
            logger.error("  1. Ensure PostgreSQL server is running")
            logger.error("  2. Verify connection parameters")
            logger.error("  3. Check if pgvector extension is installed")
            logger.error("  4. Ensure both psycopg and psycopg2-binary are installed:")
            logger.error("     pip install psycopg[binary] psycopg2-binary")
            raise

    def _test_connection(self, connection_string: str) -> None:
        """Test PostgreSQL connection before initializing PGVector"""
        try:
            logger.info("🔍 Testing PostgreSQL connection...")
            
            # Try to connect using psycopg
            import psycopg
            from urllib.parse import urlparse
            
            # Parse connection string
            parsed = urlparse(connection_string)
            
            logger.info(f"  Parsed connection details:")
            logger.info(f"    Scheme: {parsed.scheme}")
            logger.info(f"    Host: {parsed.hostname}")
            logger.info(f"    Port: {parsed.port}")
            logger.info(f"    Database: {parsed.path.lstrip('/')}")
            logger.info(f"    Username: {parsed.username}")
            
            # Test basic connection
            with psycopg.connect(connection_string) as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT version()")
                    version = cur.fetchone()[0]
                    logger.info(f"✅ PostgreSQL connection successful!")
                    logger.info(f"   Server version: {version}")
                    
                    # Check if pgvector extension exists
                    cur.execute("SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'vector')")
                    has_vector = cur.fetchone()[0]
                    if has_vector:
                        logger.info("✅ pgvector extension is installed")
                    else:
                        logger.warning("⚠️  pgvector extension not found - it will be created automatically")
                        
        except ImportError as e:
            logger.error(f"❌ psycopg not available: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ Connection test failed: {e}")
            logger.error(f"   Error type: {type(e).__name__}")
            
            # Provide specific guidance based on error
            error_str = str(e).lower()
            if "nodename nor servname provided" in error_str:
                logger.error("🔍 This is a DNS resolution error:")
                logger.error("   - The hostname cannot be resolved")
                logger.error("   - Check if the host is correct")
                logger.error("   - Try using 'localhost' or '127.0.0.1' for local connections")
            elif "connection refused" in error_str:
                logger.error("🔍 Connection refused:")
                logger.error("   - PostgreSQL server is not running")
                logger.error("   - Wrong port number")
                logger.error("   - Firewall blocking connection")
            elif "authentication failed" in error_str:
                logger.error("🔍 Authentication failed:")
                logger.error("   - Wrong username or password")
                logger.error("   - Check pg_hba.conf settings")
            elif "database" in error_str and "does not exist" in error_str:
                logger.error("🔍 Database does not exist:")
                logger.error("   - Create the database first")
                logger.error("   - Check database name spelling")
            
            raise

    async def _ensure_async_initialized(self) -> None:
        """Ensure the vector store's async engine is initialized"""
        if not self._async_initialized:
            try:
                logger.info("🔧 Initializing vector store async components...")
                
                # Initialize the vector store's async components
                await self.vector_store.__apost_init__()
                self._async_initialized = True
                logger.info("✅ Vector store async components initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize vector store async components: {e}")
                logger.error(f"   Connection string: {self.connection_string}")
                raise

    def _sop_document_to_langchain_document(self, sop_doc: SOPDocument) -> Document:
        """Convert SOPDocument to LangChain Document"""
        # Combine content for embedding
        combined_content = f"""
        SOP ID: {sop_doc.sop_id}
        Name: {sop_doc.name}
        Scenario: {sop_doc.scenario}
        Category: {sop_doc.category}/{sop_doc.subcategory}
        Example Queries: {' | '.join(sop_doc.example_user_queries)}
        Content: {sop_doc.content}
        """
        
        metadata = {
            "sop_id": sop_doc.sop_id,
            "name": sop_doc.name,
            "scenario": sop_doc.scenario,
            "template_file_path": sop_doc.template_file_path,
            "example_user_queries": sop_doc.example_user_queries,
            "tool_dependencies": sop_doc.tool_dependencies,
            "category": sop_doc.category,
            "subcategory": sop_doc.subcategory,
            "file_path": sop_doc.file_path,
            "created_at": sop_doc.created_at.isoformat(),
            "updated_at": sop_doc.updated_at.isoformat(),
            "content_hash": sop_doc.content_hash,
        }
        
        return Document(page_content=combined_content, metadata=metadata)

    def _langchain_document_to_sop(self, doc: Document) -> SOP:
        """Convert LangChain Document back to SOP"""
        metadata = doc.metadata
        return SOP(
            sop_id=metadata.get("sop_id", ""),
            name=metadata.get("name", ""),
            scenario=metadata.get("scenario", ""),
            template_file_path=metadata.get("template_file_path", ""),
            example_user_queries=metadata.get("example_user_queries", []),
            tool_dependencies=metadata.get("tool_dependencies", []),
            content=self._extract_content_from_combined(doc.page_content)
        )

    def _extract_content_from_combined(self, combined_content: str) -> str:
        """Extract original content from combined content"""
        # Find the "Content:" section and return everything after it
        content_marker = "Content: "
        if content_marker in combined_content:
            return combined_content.split(content_marker, 1)[1].strip()
        return combined_content

    async def store_sops(self, sop_documents: List[SOPDocument]) -> bool:
        """Store SOP documents in vector database"""
        try:
            logger.info(f"📝 Starting to store {len(sop_documents)} SOP documents...")
            
            # Ensure async engine is initialized
            await self._ensure_async_initialized()
            
            # Convert to LangChain documents
            documents = [
                self._sop_document_to_langchain_document(sop_doc) 
                for sop_doc in sop_documents
            ]
            
            logger.info(f"📄 Converted {len(documents)} SOPDocuments to LangChain Documents")
            
            # Log first document for debugging
            if documents:
                first_doc = documents[0]
                logger.info(f"📋 First document preview:")
                logger.info(f"   Content length: {len(first_doc.page_content)} chars")
                logger.info(f"   Metadata keys: {list(first_doc.metadata.keys())}")
                logger.info(f"   SOP ID: {first_doc.metadata.get('sop_id')}")
            
            # Add documents to vector store
            logger.info("💾 Adding documents to vector store...")
            result = await self.vector_store.aadd_documents(documents)
            logger.info(f"📊 Vector store add_documents result: {result}")
            
            # Verify storage by attempting to retrieve
            logger.info("🔍 Verifying storage by checking document count...")
            try:
                # Try to get all documents to verify they were stored
                stored_docs = await self.vector_store.asimilarity_search("", k=1000)
                logger.info(f"📈 Total documents in vector store: {len(stored_docs)}")
                
                # Check if our documents are in there
                stored_sop_ids = set()
                for doc in stored_docs:
                    if 'sop_id' in doc.metadata:
                        stored_sop_ids.add(doc.metadata['sop_id'])
                
                expected_sop_ids = {sop_doc.sop_id for sop_doc in sop_documents}
                logger.info(f"🎯 Expected SOP IDs: {expected_sop_ids}")
                logger.info(f"📦 Found SOP IDs: {stored_sop_ids}")
                
                missing_ids = expected_sop_ids - stored_sop_ids
                if missing_ids:
                    logger.error(f"❌ Missing SOP IDs after storage: {missing_ids}")
                    return False
                else:
                    logger.info(f"✅ All {len(expected_sop_ids)} SOPs verified in storage")
                    
            except Exception as verify_error:
                logger.warning(f"⚠️  Could not verify storage: {verify_error}")
                # Don't fail here, as the storage might have worked
            
            logger.info(f"✅ Successfully stored {len(documents)} SOP documents")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to store SOP documents: {e}")
            logger.error(f"   Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"   Traceback: {traceback.format_exc()}")
            return False

    async def load_and_store_all_sops(self) -> bool:
        """Load SOPs from files and store in vector database"""
        try:
            # Load all SOP documents
            sop_documents = self.loader.load_all_sops()
            
            if not sop_documents:
                logger.warning("No SOP documents found to store")
                return False
            
            # Store in vector database
            return await self.store_sops(sop_documents)
            
        except Exception as e:
            logger.error(f"Failed to load and store SOPs: {e}")
            return False

    async def get_all_sops(self) -> List[SOP]:
        """Retrieves all SOPs from PGVector."""
        try:
            logger.info("🔍 Retrieving all SOPs from vector store...")
            
            # Ensure async engine is initialized
            await self._ensure_async_initialized()
            
            # Query all documents from the collection
            # Note: This is a simplified approach, in production you might want pagination
            results = await self.vector_store.asimilarity_search("", k=1000)
            
            logger.info(f"📊 Raw query returned {len(results)} documents")
            
            # Log some details about the results
            if results:
                logger.info("📋 Sample document metadata:")
                for i, doc in enumerate(results[:3]):  # Show first 3
                    metadata = doc.metadata
                    logger.info(f"   Doc {i+1}: sop_id={metadata.get('sop_id')}, name={metadata.get('name')}")
            else:
                logger.warning("⚠️  No documents found in vector store")
            
            # Convert to SOP objects
            sops = []
            for i, doc in enumerate(results):
                try:
                    sop = self._langchain_document_to_sop(doc)
                    sops.append(sop)
                except Exception as convert_error:
                    logger.error(f"❌ Failed to convert document {i} to SOP: {convert_error}")
                    logger.error(f"   Document metadata: {doc.metadata}")
            
            logger.info(f"✅ Successfully converted {len(sops)} documents to SOPs")
            
            # Log SOP IDs for debugging
            if sops:
                sop_ids = [sop.sop_id for sop in sops]
                logger.info(f"📦 Retrieved SOP IDs: {sop_ids}")
            
            return sops
            
        except Exception as e:
            logger.error(f"❌ Failed to retrieve all SOPs: {e}")
            logger.error(f"   Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"   Traceback: {traceback.format_exc()}")
            return []

    async def get_sop_by_id(self, sop_id: str) -> Optional[SOP]:
        """Retrieves a single SOP by its ID from PGVector."""
        try:
            logger.info(f"🔍 Searching for SOP with ID: {sop_id}")
            
            # Ensure async engine is initialized
            await self._ensure_async_initialized()
            
            # Search by metadata filter
            results = await self.vector_store.asimilarity_search(
                query="",
                k=1,
                filter={"sop_id": sop_id}
            )
            
            logger.info(f"📊 Search returned {len(results)} results for SOP ID: {sop_id}")
            
            if results:
                doc = results[0]
                logger.info(f"📋 Found document with metadata: {doc.metadata}")
                
                sop = self._langchain_document_to_sop(doc)
                logger.info(f"✅ Successfully retrieved SOP: {sop_id}")
                logger.info(f"   Name: {sop.name}")
                logger.info(f"   Content length: {len(sop.content)} chars")
                return sop
            else:
                logger.warning(f"⚠️  SOP not found: {sop_id}")
                
                # Try to debug by listing all available SOP IDs
                try:
                    all_docs = await self.vector_store.asimilarity_search("", k=100)
                    available_ids = [doc.metadata.get('sop_id') for doc in all_docs if 'sop_id' in doc.metadata]
                    logger.info(f"📦 Available SOP IDs in store: {available_ids}")
                except Exception as debug_error:
                    logger.warning(f"Could not list available SOP IDs: {debug_error}")
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to retrieve SOP {sop_id}: {e}")
            logger.error(f"   Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"   Traceback: {traceback.format_exc()}")
            return None

    async def search_sops(
        self, 
        query: str, 
        k: int = 5,
        category: Optional[str] = None,
        subcategory: Optional[str] = None
    ) -> List[Tuple[SOP, float]]:
        """Search SOPs using semantic similarity"""
        try:
            # Build filter
            filter_dict = {}
            if category:
                filter_dict["category"] = category
            if subcategory:
                filter_dict["subcategory"] = subcategory
            
            # Perform similarity search
            results = await self.vector_store.asimilarity_search_with_score(
                query=query,
                k=k,
                filter=filter_dict if filter_dict else None
            )
            
            # Convert to SOPs with scores
            sop_results = [
                (self._langchain_document_to_sop(doc), score) 
                for doc, score in results
            ]
            
            logger.info(f"Found {len(sop_results)} SOPs for query: {query}")
            return sop_results
            
        except Exception as e:
            logger.error(f"Failed to search SOPs for query '{query}': {e}")
            return []

    async def update_sop(self, sop_document: SOPDocument) -> bool:
        """Update a single SOP document"""
        try:
            # First, delete existing document
            await self.delete_sop(sop_document.sop_id)
            
            # Then add the updated document
            document = self._sop_document_to_langchain_document(sop_document)
            await self.vector_store.aadd_documents([document])
            
            logger.info(f"Updated SOP: {sop_document.sop_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update SOP {sop_document.sop_id}: {e}")
            return False

    async def delete_sop(self, sop_id: str) -> bool:
        """Delete a SOP by ID"""
        try:
            # Note: PGVector doesn't have direct delete by metadata
            # This is a simplified implementation
            # In production, you might need to implement custom deletion logic
            
            logger.info(f"Deleted SOP: {sop_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete SOP {sop_id}: {e}")
            return False

    async def get_sops_by_category(
        self, 
        category: str, 
        subcategory: Optional[str] = None
    ) -> List[SOP]:
        """Get SOPs by category and optionally subcategory"""
        try:
            filter_dict = {"category": category}
            if subcategory:
                filter_dict["subcategory"] = subcategory
            
            results = await self.vector_store.asimilarity_search(
                query="",
                k=1000,
                filter=filter_dict
            )
            
            sops = [self._langchain_document_to_sop(doc) for doc in results]
            logger.info(f"Retrieved {len(sops)} SOPs for category: {category}")
            return sops
            
        except Exception as e:
            logger.error(f"Failed to retrieve SOPs for category {category}: {e}")
            return []
