"""
DashScope Embedding Service
Integrates with Alibaba DashScope text-embedding-v4 model.
This simplified version removes the complex fallback embedding generation
and improves batch processing.
"""
import asyncio
import logging
import os
from typing import List, Optional

import numpy as np
import dashscope
from dashscope import TextEmbedding
from deep_diagnose.common.config import get_config

logger = logging.getLogger(__name__)


def _get_embedding_config():
    """从配置系统获取embedding配置"""
    config = get_config()
    return getattr(config, 'embedding', None)

class DashScopeEmbeddingService:
    """
    A service to generate text embeddings using DashScope.
    It supports batch processing.
    """

    def __init__(self, model_name: Optional[str] = None, api_key: Optional[str] = None, dimension: Optional[int] = None):
        # 获取配置
        embedding_config = _get_embedding_config()
        
        # 优先使用参数，其次是配置，最后是默认值
        self.model_name = model_name or (embedding_config.model if embedding_config else "text-embedding-v4")
        self.api_key = api_key or (embedding_config.api_key if embedding_config else None)
        self.dimension = dimension or (embedding_config.dimension if embedding_config else 2048)
        
        self._initialize_dashscope()

    def _initialize_dashscope(self):
        """Initializes the DashScope API key."""
        api_key = self.api_key or os.getenv('DASHSCOPE_API_KEY')
        if api_key:
            dashscope.api_key = api_key
            logger.info(f"DashScope embedding service initialized with model: {self.model_name}")
        else:
            logger.warning("DashScope API key not found in config or environment variables.")

    async def get_embedding(self, text: str) -> np.ndarray:
        """Gets the embedding for a single text."""
        if not text or not text.strip():
            return np.zeros(self.dimension)  # 使用配置的维度

        embeddings = await self._generate_embeddings([text])
        return embeddings[0]

    async def get_embeddings_batch(self, texts: List[str]) -> List[np.ndarray]:
        """Gets embeddings for a batch of texts."""
        if not texts:
            return []
        
        return await self._generate_embeddings(texts)

    async def _generate_embeddings(self, texts: List[str]) -> List[np.ndarray]:
        """Generates embeddings for a list of texts using the DashScope API."""
        if not dashscope.api_key:
            raise RuntimeError("DashScope API key is not configured.")

        loop = asyncio.get_event_loop()
        
        try:
            response = await loop.run_in_executor(
                None,
                lambda: TextEmbedding.call(
                    model=self.model_name,
                    input=texts,
                    dimensions=2048,
                    timeout=30,
                )
            )

            if response.status_code == 200:
                embeddings = [
                    np.array(e['embedding'], dtype=np.float32)
                    for e in response.output['embeddings']
                ]
                return embeddings
            else:
                raise RuntimeError(f"DashScope API error: {response.message} (code: {response.status_code})")
        except Exception as e:
            logger.error(f"Failed to generate DashScope embeddings: {e}")
            raise

    @staticmethod
    def calculate_similarity(embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """Calculates the cosine similarity between two embeddings."""
        if embedding1.size == 0 or embedding2.size == 0:
            return 0.0

        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        similarity = dot_product / (norm1 * norm2)
        return max(0.0, min(1.0, float(similarity)))

# Global instance
_embedding_service: Optional[DashScopeEmbeddingService] = None

def get_embedding_service(model_name: Optional[str] = None, api_key: Optional[str] = None, dimension: Optional[int] = None) -> DashScopeEmbeddingService:
    """
    Returns a singleton instance of the DashScopeEmbeddingService.
    如果不提供参数，将从配置系统中获取。
    默认使用 text-embedding-v4 模型。
    """
    global _embedding_service
    if _embedding_service is None:
        _embedding_service = DashScopeEmbeddingService(model_name, api_key, dimension)
    return _embedding_service