import logging
import json
import re
from typing import List, Dict, Any

from deep_diagnose.core.reasoning.planning.models import SOP, SelectedSOP
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
from .base import SOPSelectionStrategy

logger = logging.getLogger(__name__)


class LLMSelectionStrategy(SOPSelectionStrategy):
    """SOP selection strategy that uses an LLM to choose the best SOP."""

    def __init__(self, llm_client: Any = None):
        self.llm = llm_client if llm_client else get_llm_by_type(AGENT_LLM_MAP.get("planner", "reasoning"))
        if not self.llm:
            logger.error("LLM client not initialized for LLMSelectionStrategy.")
            raise ValueError("LLM client is required for LLMSelectionStrategy.")

    async def select_sop(self, query: str, sops: List[SOP]) -> SelectedSOP:
        if not sops:
            return SelectedSOP(sop=None, reason="No SOPs available for selection.", success=False)

        try:
            prompt = self._build_llm_prompt(query, sops)
            messages = [{"role": "user", "content": prompt}]
            response = await self.llm.ainvoke(messages, config={"stream": False, "tags": ["silent_llm_call"]})

            result = self._parse_llm_response(response.content)
            selected_index = result.get("index", 0)
            reason = result.get("reason", "LLM selection")

            if 0 <= selected_index < len(sops):
                selected_sop = sops[selected_index]
                return SelectedSOP(sop=selected_sop, reason=f"LLM选择: {reason}", success=True)
            else:
                # Fallback to the first SOP if index is invalid
                fallback_sop = sops[0]
                return SelectedSOP(sop=fallback_sop, reason=f"LLM返回无效索引，使用默认SOP: {reason}", success=True)

        except Exception as e:
            logger.error(f"LLM selection failed: {e}", exc_info=True)
            # Fallback to the first SOP on error
            fallback_sop = sops[0]
            return SelectedSOP(sop=fallback_sop, reason=f"LLM选择失败，使用默认SOP: {str(e)}", success=False, error_message=str(e))

    def _build_llm_prompt(self, query: str, sops: List[SOP]) -> str:
        options = []
        for i, sop in enumerate(sops):
            scenario_text = sop.scenario.replace('\n', ' ').strip() if isinstance(sop.scenario, str) else '无场景描述'
            if len(scenario_text) > 200:
                scenario_text = scenario_text[:200] + "..."

            example_text = '、'.join(sop.example_user_queries[:2]) if sop.example_user_queries else '无示例'

            option_info = f"""
选项{i}: {sop.name}
  场景: {scenario_text}
  示例: {example_text}"""
            options.append(option_info)

        prompt = f"""你是SOP选择专家，需要根据用户问题选择最合适的诊断方案。

【用户问题】
{query}

【可选SOP方案】
{chr(10).join(options)}

【选择要求】
1. 仔细分析用户问题的关键特征
2. 对比每个SOP的适用场景和识别规则
3. 选择最匹配的SOP方案
4. 提供清晰的选择理由
5. 如果具体sop不满足条件，应该选择 general_troubleshooting sop

【输出格式】
请严格按照以下JSON格式返回结果：
{{"index": 选择的SOP索引号, "reason": "详细的选择理由"}} """
        return prompt

    def _parse_llm_response(self, content: str) -> Dict:
        try:
            content = content.strip()
            json_patterns = [
                r'{{[^{{}}]*"index"[^{{}}]*"reason"[^{{}}]*}}',
                r'{{.*"index".*"reason".*?}}',
                r'{{.*?}}',
            ]

            for pattern in json_patterns:
                json_match = re.search(pattern, content, re.DOTALL)
                if json_match:
                    try:
                        result = json.loads(json_match.group(0))
                        if "index" in result:
                            if "reason" not in result:
                                result["reason"] = "LLM选择"
                            return result
                    except json.JSONDecodeError:
                        continue

            index_match = re.search(r'(?:index|选择|索引).*?(\d+)', content, re.IGNORECASE)
            if index_match:
                index = int(index_match.group(1))
                return {"index": index, "reason": f"从响应中提取索引: {index}"}

        except Exception as e:
            logger.warning(f"LLM response JSON parsing failed: {e}, content: {content[:100]}")

        return {"index": 0, "reason": "Parsing failed, using default option"}
