"""
Reporter 模板管理器 - 动态选择并注入 Reporter 模板

参考 SOPManager 的实现，使用 LLM 从配置的模板中选择最合适的一个，并加载其内容。
"""

import json
import logging
from typing import Any, Dict, Tuple, List

from .reporter_template_query import get_reporter_template_query

logger = logging.getLogger(__name__)


class ReporterTemplateManager:
    """Reporter 模板管理器"""

    def __init__(self):
        self.query = get_reporter_template_query()

    async def select_and_load(self, context: Dict[str, Any]) -> Tuple[str, str, str]:
        """选择并加载模板

        Returns: (content, name, reason)
        """
        configs = self.query.get_configs()
        if not configs:
            return "", "", "没有可用的Reporter模板配置"

        # 规则优先：根据 sop_name 精确匹配
        try:
            sop_name = context.get("sop_name", "")
            if sop_name:
                sop_matched = [c for c in configs if sop_name in (c.get("related_sop_names") or [])]
                if sop_matched:
                    chosen = sop_matched[0]
                    content = self.query.load_template_content(chosen.get("template_file", ""))
                    name = chosen.get("display_name", "未知模板")
                    return content, name, f"规则匹配(sop_name={sop_name})"
        except Exception:
            pass

        # 回退到LLM选择
        selected, reason = await self._select(configs, context)
        content = self.query.load_template_content(selected.get("template_file", ""))
        name = selected.get("display_name", "未知模板")
        return content, name, reason

    async def _select(self, configs: List[Dict[str, Any]], context: Dict[str, Any]) -> Tuple[Dict[str, Any], str]:
        """基于上下文让LLM选择模板"""
        try:
            # 构建可选项概览
            options = []
            for i, cfg in enumerate(configs):
                name = cfg.get("display_name", f"选项{i}")
                examples = cfg.get("example_queries", [])
                example_text = ", ".join(examples[:2]) if examples else "无示例"
                options.append(f"{i}: {name} (示例: {example_text})")

            # 上下文信息
            sop_name = context.get("sop_name", "")
            plan_title = ""
            if isinstance(context.get("current_plan"), dict):
                plan_title = context.get("current_plan", {}).get("title", "")
            else:
                plan = context.get("current_plan")
                try:
                    plan_title = getattr(plan, "title", "")
                except Exception:
                    plan_title = ""

            # 从消息中提取最近用户提问
            user_query = ""
            for m in reversed(context.get("messages", [])):
                try:
                    role = getattr(m, "type", None) or getattr(m, "role", None) or getattr(m, "name", None)
                    content = getattr(m, "content", None) or m.get("content")
                    if role in ("human", "user") and content:
                        user_query = content
                        break
                except Exception:
                    continue

            prompt = f"""你是报告模板选择器。根据上下文内容，从下列模板中选择一个最合适的：

上下文：
- SOP名称: {sop_name}
- 计划标题: {plan_title}
- 用户问题: {user_query}

可选模板：
{chr(10).join(options)}

按以下JSON格式返回选择结果：{{"index": 0, "reason": "选择理由"}}
"""

            from deep_diagnose.llms.llm import get_llm_by_type
            from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP

            llm = get_llm_by_type(AGENT_LLM_MAP.get("planner", "reasoning"))
            messages = [{"role": "user", "content": prompt}]
            response = await llm.ainvoke(messages, config={"stream": False, "tags": ["silent_llm_call"]})

            result = self._parse_response(response.content)
            index = result.get("index", 0)
            reason = result.get("reason", "LLM选择")

            if 0 <= index < len(configs):
                return configs[index], f"LLM选择: {reason}"
            else:
                return configs[0], f"索引无效，使用默认: {reason}"

        except Exception as e:
            logger.error(f"Reporter 模板选择失败: {e}")
            return configs[0], f"LLM失败，使用默认: {str(e)}"

    def _parse_response(self, content: str) -> Dict[str, Any]:
        try:
            import re
            json_match = re.search(r"\{.*?\}", content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(0))
        except Exception:
            pass
        return {"index": 0, "reason": "解析失败"}


# 全局单例
_manager: ReporterTemplateManager | None = None

def get_reporter_template_manager() -> ReporterTemplateManager:
    global _manager
    if _manager is None:
        _manager = ReporterTemplateManager()
    return _manager


async def select_and_load_template(context: Dict[str, Any]) -> Tuple[str, str, str]:
    return await get_reporter_template_manager().select_and_load(context)