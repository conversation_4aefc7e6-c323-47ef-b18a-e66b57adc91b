"""
聊天领域仓储层
处理聊天会话和消息的数据库操作
"""

import logging
from typing import List, Optional
from uuid import uuid4
from functools import wraps

from .models import CloudbotAgentChatSession, CloudbotAgentChatMessage, MessageStatus
from .schemas import ChatSessionCreate, ChatMessageCreate, ChatMessageUpdate

logger = logging.getLogger(__name__)


def ensure_db_connection(func):
    """装饰器：确保数据库连接在方法执行前已初始化"""

    @wraps(func)
    async def wrapper(self, *args, **kwargs):
        await self._ensure_db_connection()
        return await func(self, *args, **kwargs)

    return wrapper


class ChatRepository:
    """聊天数据仓储"""

    def __init__(self):
        """初始化仓储"""
        self._db_manager = None

    @property
    def db_manager(self):
        """延迟导入数据库管理器，避免循环导入"""
        if self._db_manager is None:
            from deep_diagnose.data.database import db_manager

            self._db_manager = db_manager
        return self._db_manager

    async def _ensure_db_connection(self):
        """确保数据库连接已初始化"""
        from tortoise import connections

        try:
            # 检查默认连接是否存在且可用
            conn = connections.get("default")
            if conn is None or not self.db_manager.is_initialized:
                raise Exception("Connection not properly initialized")

            # 测试连接是否可用
            await conn.execute_query("SELECT 1")

        except Exception as e:
            # 如果连接不存在或不可用，重新初始化数据库
            logger.warning(f"Database connection issue detected: {e}, reinitializing...")

            try:
                # 如果已经初始化过，先关闭现有连接
                if self.db_manager.is_initialized:
                    await self.db_manager.close_database()

                # 重新初始化数据库连接
                await self.db_manager.init_database(generate_schemas=False)
                logger.info("Database connection reinitialized successfully")

            except Exception as init_error:
                logger.error(f"Failed to reinitialize database connection: {init_error}")
                raise

    @ensure_db_connection
    async def create_session(self, user_id: str, subject: str = "", session_id: str | None = "", instance_id: str = "") -> CloudbotAgentChatSession:
        """创建新的聊天会话
        Args:
            user_id: 员工工号
            title: 会话标题
            instance_id: 诊断实例ID
            session_id: 会话id
        Returns:
            创建的会话对象
        """
        session_id = session_id or str(uuid4())

        session_data = ChatSessionCreate(session_id=session_id, user_id=user_id, subject=subject)

        session = await CloudbotAgentChatSession.create(**session_data.model_dump())
        logger.info(f"Created new chat session: {session.session_id} for user: {user_id}")

        return session

    @ensure_db_connection
    async def get_session_by_id(self, session_id: str) -> Optional[CloudbotAgentChatSession]:
        """根据session_id获取会话

        Args:
            session_id: 会话ID

        Returns:
            会话对象或None
        """
        return await CloudbotAgentChatSession.filter(session_id=session_id).first()

    @ensure_db_connection
    async def get_session_messages(self, session_id: str) -> List[CloudbotAgentChatMessage]:
        """获取会话的所有消息

        Args:
            session_id: 会话ID

        Returns:
            消息列表，按创建时间排序
        """
        messages = await CloudbotAgentChatMessage.filter(session_id=session_id).order_by("gmt_create")

        return messages

    @ensure_db_connection
    async def create_message(
        self, session_id: str, message: str, message_type: str, request_id: str, agent: str = "", status: str = MessageStatus.EXECUTING.value, ext: dict = {}
    ) -> CloudbotAgentChatMessage:
        """创建新消息

        Args:
            session_id: 会话ID
            message: 消息内容
            message_type: 消息类型 (human_query/ai_response/auto_query)
            request_id: 请求ID
            agent: 消息处理 Agent 名称
            status: 消息状态 (executing/success/failed)
            ext: 扩展信息

        Returns:
            创建的消息对象
        """
        message_data = ChatMessageCreate(
            session_id=session_id,
            message=message,
            message_type=message_type,
            request_id=request_id,
            agent=agent,
            status=status,
            ext=ext,
        )

        message_obj = await CloudbotAgentChatMessage.create(**message_data.model_dump())
        logger.info(f"Created message {message_obj.id} for session {session_id}, type: {message_type}")

        return message_obj

    @ensure_db_connection
    async def update_message(self, message_id: int, **kwargs) -> Optional[CloudbotAgentChatMessage]:
        """更新消息

        Args:
            message_id: 消息ID
            **kwargs: 要更新的字段

        Returns:
            更新后的消息对象或None
        """
        message = await CloudbotAgentChatMessage.filter(id=message_id).first()
        if message:
            await message.update_from_dict(kwargs)
            await message.save()
            logger.info(f"Updated message {message_id}")
            return message

        logger.warning(f"Message {message_id} not found for update")
        return None

    async def update_message_content(self, message_id: int, content: str) -> Optional[CloudbotAgentChatMessage]:
        """更新消息内容

        Args:
            message_id: 消息ID
            content: 新的消息内容

        Returns:
            更新后的消息对象或None
        """
        return await self.update_message(message_id, message=content)

    async def mark_message_completed(self, message_id: int) -> Optional[CloudbotAgentChatMessage]:
        """标记消息为已完成

        Args:
            message_id: 消息ID

        Returns:
            更新后的消息对象或None
        """
        return await self.update_message(message_id, status=MessageStatus.SUCCESS.value)

    @ensure_db_connection
    async def get_user_sessions(
        self, user_id: str, start_time: Optional[str] = None, end_time: Optional[str] = None, limit: int = 20
    ) -> List[CloudbotAgentChatSession]:
        """获取用户的聊天会话列表

        Args:
            user_id: 用户ID
            start_time: 开始时间 (ISO格式字符串)
            end_time: 结束时间 (ISO格式字符串)
            limit: 返回数量限制

        Returns:
            会话列表，按创建时间倒序
        """
        query = CloudbotAgentChatSession.filter(user_id=user_id)
        query = CloudbotAgentChatSession.filter(user_id=user_id)
        if start_time:
            from datetime import datetime

            start_dt = datetime.fromisoformat(start_time.replace("Z", "+00:00"))
            query = query.filter(gmt_create__gte=start_dt)

        if end_time:
            from datetime import datetime

            end_dt = datetime.fromisoformat(end_time.replace("Z", "+00:00"))
            query = query.filter(gmt_create__lte=end_dt)

        sessions = await query.order_by("-gmt_create").limit(limit)
        logger.info(f"Retrieved {len(sessions)} sessions for user {user_id}")

        return sessions


# 全局仓储实例
chat_repository = ChatRepository()
