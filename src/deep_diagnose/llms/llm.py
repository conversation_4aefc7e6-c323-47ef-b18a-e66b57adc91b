from langchain_openai import Chat<PERSON>penA<PERSON>

from deep_diagnose.common.config import get_config
from deep_diagnose.common.config.constants.agents import LLMType
from deep_diagnose.common.config.core.base_config import DotD<PERSON>
from deep_diagnose.llms.chat_qwen import Chat<PERSON>wen

# Cache for LLM instances
_llm_cache: dict[LLMType, ChatOpenAI] = {}


def _create_llm_use_conf(llm_type: LLMType, conf: DotDict) -> ChatOpenAI:
    llm_type_map = {
        "reasoning": conf.llm.profiles.reasoning,
        "basic": conf.llm.profiles.basic,
        "vision": conf.llm.profiles.vision,
        "code_flash": conf.llm.profiles.code_flash,
        "glm": conf.llm.profiles.glm,
        "code": getattr(conf.llm.profiles, 'code', conf.llm.profiles.reasoning),
    }
    llm_conf = llm_type_map.get(llm_type)
    if not llm_conf:
        raise ValueError(f"Unknown LLM type: {llm_type}")
    if not isinstance(llm_conf, dict):
        raise ValueError(f"Invalid LLM Conf: {llm_type}")
    return ChatQwen(**llm_conf)


def get_llm_by_type(
    llm_type: LLMType,
) -> ChatOpenAI:
    """
    Get LLM instance by type. Returns cached instance if available.
    """
    if llm_type in _llm_cache:
        return _llm_cache[llm_type]
    conf = get_config()
    llm = _create_llm_use_conf(llm_type, conf)
    _llm_cache[llm_type] = llm
    return llm


