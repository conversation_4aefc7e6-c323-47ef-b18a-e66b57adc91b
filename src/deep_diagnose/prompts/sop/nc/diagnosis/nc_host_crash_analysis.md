
# SOP：基于vmcore分析的物理机（NC）宕机根因诊断

## 诊断步骤（必须按顺序执行）

### 步骤1：获取宕机基础信息
**目标**：提取指定节点（NC）的宕机关键日志信息，确定宕机特征
- **工具**：`getNcDownRecord`
- **输入参数**：NC IP 或 NC SN（只需要输入NC信息，不需要输入时间范围、不要开始时间、不要结束时间）
- **分析要点**：
  - 统计宕机次数（根据crash time记录）
  - 记录所有精确的宕机时间点
  - 分析crash log关键信息，初步推断宕机原因
- **关键输出内容**：
  - 宕机总次数：X次
  - 与用户问题相关的宕机时间：YYYY-MM-DD HH:MM:SS
  - 初步宕机原因分析：基于crash log的初步判断

### 步骤2：内核层面vmcore分析
**目标**：从内核层面诊断宕机根本原因
- **工具**：`analyzeVmcoreFromCore`
- **输入参数**：NC IP（使用步骤1获取的NC信息）、时间范围为:（startTime：宕机时间，endTime：宕机时间 +15天 (15day，宕机之后15天) ）
- **分析逻辑**：
  - 返回内容为空 → 初步排除内核导致的宕机
  - 返回内容不为空 → 深入分析内核错误信息，建立内核错误与宕机的证据链
- **关键输出内容**：
  - 内核分析状态：有内核异常/无内核异常
  - 内核错误类型：panic/oops/其他（如有）
  - 内核错误详情：具体错误信息和调用栈
  - 内核层面结论：是否为内核导致的宕机及证据

### 步骤3：虚拟化层面vmcore分析
**目标**：从虚拟化层面诊断宕机根本原因
- **工具**：`analyzeVmcoreFromVirt`
- **输入参数**：NC IP（使用步骤1获取的NC信息）、时间范围为:（startTime：宕机时间，endTime：宕机时间 +15天(15day，宕机之后15天) ）
- **分析逻辑**：
  - 返回内容为空 → 初步排除虚拟化导致的宕机
  - 返回内容不为空 → 深入分析虚拟化错误信息，建立虚拟化错误与宕机的证据链
- **关键输出内容**：
  - 虚拟化分析状态：有虚拟化异常/无虚拟化异常
  - 虚拟化错误类型：hypervisor错误/VM管理错误/其他（如有）
  - 虚拟化错误详情：具体错误信息和相关组件
  - 虚拟化层面结论：是否为虚拟化导致的宕机及证据
### 步骤4：硬件层面vmcore分析
**目标**：从硬件层面诊断宕机根本原因
- **工具**：`analyzeVmcoreFromJinlun`
- **输入参数**：NC IP（使用步骤1获取的NC信息）、时间范围为:（startTime：宕机时间，endTime：宕机时间 +15天 (15day，宕机之后15天)）
- **分析逻辑**：
  - 返回内容为空 → 初步排除硬件故障导致的宕机
  - 返回内容不为空 → 深入分析硬件故障信息，建立硬件故障与宕机的证据链
- **关键输出内容**：
  - 硬件分析状态：有硬件异常/无硬件异常
  - 硬件故障类型：内存错误/CPU错误/IO错误/其他（如有）
  - 硬件故障详情：具体故障信息
  - 硬件层面结论：是否为硬件故障导致的宕机及证据

### 步骤5：获取宕机前VM列表
**目标**：查询NC在宕机前1小时内的所有VM列表
- **工具**：`listVmsOnNc`
- **输入参数**：NC IP，时间范围（宕机前1小时，基于步骤1获取的宕机时间）
- **分析逻辑**：
  - 返回内容为空 → 该NC上无VM运行
  - 返回内容不为空 → 提取所有实例InstanceId，为下一步分析做准备
- **关键输出内容**：
  - VM实例列表：[i-xxx, i-yyy, i-zzz]

### 步骤6：分析VM变更操作
**目标**：检查NC上所有VM是否存在高危变更操作
- **工具**：`listChangeRecords`
- **输入参数**：步骤5获取的所有VM列表
- **分析要点**：
  - 识别高危变更操作类型
  - 分析变更时间与宕机时间的关联性
  - 评估变更操作与宕机的因果关系
- **关键输出内容**：
  - 变更记录总数：X条变更
  - 高危变更类型：热迁移/重启/配置变更/其他
  - 变更时间与宕机时间关联：变更时间 vs 宕机时间
  - 变更影响评估：是否可能导致宕机及原因分析

### 步骤7：分析NC变更操作
**目标**：检查NC本身是否存在高危变更操作
- **工具**：`listChangeRecords`
- **输入参数**：NC标识（IP或SN）
- **时间范围**：宕机前1小时（基于步骤1获取的宕机时间）
- **分析要点**：
  - 识别NC级别的变更操作
  - 分析变更时间与宕机时间的关联性
  - 评估NC变更与宕机的因果关系
- **关键输出内容**：
  - NC变更记录总数：X条变更
  - NC变更类型：硬件维护/系统更新/配置变更/其他
  - 变更时间与宕机时间关联：变更时间 vs 宕机时间
  - NC变更影响评估：是否可能导致宕机及原因分析
