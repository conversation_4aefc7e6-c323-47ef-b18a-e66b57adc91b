"""
Unit tests for InspectAgent
"""

import asyncio
import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch, MagicMock
import json
from datetime import datetime, timed<PERSON>ta

from deep_diagnose.core.interactive.agents.inspect import InspectAgent
from deep_diagnose.core.interactive.types.state import InspectState
from deep_diagnose.core.events.inspect_event import Inspect<PERSON>vent
from deep_diagnose.common.utils.machine_utils import MachineIdType
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPT<PERSON><PERSON>anager
from deep_diagnose.storage.redis_client import RedisClient


class TestInspectAgent:
    """Test cases for InspectAgent"""

    def test_inspect_agent_initialization(self):
        """Test InspectAgent initialization"""
        agent = InspectAgent({})
        assert isinstance(agent, InspectAgent)
        assert hasattr(agent, 'mcp_tool_whitelist')
        assert hasattr(agent, 'redis_client')
        # Check that the whitelist contains expected tools
        expected_tools = [
            "getVmBasicInfo",
            "getNcBasicInfo",
            "listCategorizedMonitorExceptions",
            "listReportedOperationalEvents",
            "listOperationRecords",
        ]
        assert agent.mcp_tool_whitelist == expected_tools

    @pytest.mark.asyncio
    async def test_astream_method_exists(self):
        """Test that astream method exists"""
        agent = InspectAgent({})
        assert hasattr(agent, 'astream')

    def test_prompt_generate_method_exists(self):
        """Test that _prompt_generate method exists"""
        agent = InspectAgent({})
        assert hasattr(agent, '_prompt_generate')

    def test_concurrent_query_method_exists(self):
        """Test that _concurrent_query method exists"""
        agent = InspectAgent({})
        assert hasattr(agent, '_concurrent_query')

    @pytest.mark.asyncio
    async def test_prompt_generate_with_vm_info(self):
        """Test prompt generation with VM information"""
        agent = InspectAgent({})
        
        # Create a mock state with VM information
        state = InspectState(
            machine_id="i-test123",
            start_time="2025-07-28 00:00:00",
            end_time="2025-07-30 23:59:59",
            machine_id_type=MachineIdType.INSTANCE_ID,
            tool_map={},
            info_data={
                "getVmBasicInfo": [{
                    "instanceId": "i-test123",
                    "isLocalDisk": "local",
                    "imageName": "test-image",
                    "instanceType": "ecs.g6.large",
                    "isWin": "Linux",
                    "osVersion": "CentOS 7.6",
                    "ecsBusinessStatus": "Running",
                    "status": "running"
                }],
                "listReportedOperationalEvents": [],
                "listCategorizedMonitorExceptions": [],
                "listOperationRecords": []
            },
            finished=False,
            recommendations=[],
            overview="",
        )
        
        prompt = agent._prompt_generate(state)
        assert "实例ID为 i-test123" in prompt
        assert "磁盘类型是 local" in prompt
        assert "镜像名称为 test-image" in prompt

    @pytest.mark.asyncio
    async def test_prompt_generate_with_nc_info(self):
        """Test prompt generation with NC information"""
        agent = InspectAgent({})
        
        # Create a mock state with NC information
        state = InspectState(
            machine_id="***********",
            start_time="2025-07-28 00:00:00",
            end_time="2025-07-30 23:59:59",
            machine_id_type=MachineIdType.NC_IP,
            tool_map={},
            info_data={
                "getNcBasicInfo": [{
                    "isLocalDisk": "local",
                    "cpuModel": "Intel Xeon",
                    "lockType": "none",
                    "productName": "test-product",
                    "multiCnNum": "1",
                    "cpuGeneration": "Skylake",
                    "osType": "Linux",
                    "bizStatus": "Running",
                    "dragonboxNc": "false",
                    "vcpuMod": "normal",
                    "physicalModel": "physical",
                    "lockReason": "none",
                    "grayBizType": "false"
                }],
                "listReportedOperationalEvents": [],
                "listCategorizedMonitorExceptions": [],
                "listOperationRecords": []
            },
            finished=False,
            recommendations=[],
            overview="",
        )
        
        prompt = agent._prompt_generate(state)
        assert "磁盘类型是 local" in prompt
        assert "CPU型号为 Intel Xeon" in prompt
        assert "锁定类型为 none" in prompt

    @pytest.mark.asyncio
    async def test_prompt_generate_with_events(self):
        """Test prompt generation with operational events"""
        agent = InspectAgent({})
        
        # Create a mock state with events
        state = InspectState(
            machine_id="i-test123",
            start_time="2025-07-28 00:00:00",
            end_time="2025-07-30 23:59:59",
            machine_id_type=MachineIdType.INSTANCE_ID,
            tool_map={},
            info_data={
                "getVmBasicInfo": {
                    "instanceId": "i-test123",
                },
                "listReportedOperationalEvents": [
                    {
                        "instance": "i-test123",
                        "reason": "maintenance",
                        "code_desc": "restart",
                        "status": "planned",
                        "plan": "2025-07-29 10:00:00",
                        "publish": "2025-07-28 09:00:00",
                        "end": "2025-07-29 11:00:00"
                    }
                ],
                "listCategorizedMonitorExceptions": [],
                "listOperationRecords": []
            },
            finished=False,
            recommendations=[],
            overview="",
        )
        
        prompt = agent._prompt_generate(state)
        assert "运维事件" in prompt
        assert "maintenance" in prompt
        assert "restart" in prompt

    @pytest.mark.asyncio
    async def test_prompt_generate_with_exceptions(self):
        """Test prompt generation with monitor exceptions"""
        agent = InspectAgent({})
        
        # Create a mock state with exceptions
        state = InspectState(
            machine_id="i-test123",
            start_time="2025-07-28 00:00:00",
            end_time="2025-07-30 23:59:59",
            machine_id_type=MachineIdType.INSTANCE_ID,
            tool_map={},
            info_data={
                "getVmBasicInfo": {
                    "instanceId": "i-test123",
                },
                "listReportedOperationalEvents": [],
                "listCategorizedMonitorExceptions": {
                    "keymetric": [
                        {
                            "exceptionName": "CPU_HIGH",
                            "exceptionDesc": "High CPU usage",
                            "exceptionTime": "2025-07-29 10:00:00",
                            "lastExceptionTime": "2025-07-29 10:30:00",
                            "exceptionCount": 5,
                            "reason": "High load",
                            "additionalInfo": "Process overload"
                        }
                    ]
                },
                "listOperationRecords": []
            },
            finished=False,
            recommendations=[],
            overview="",
        )
        
        prompt = agent._prompt_generate(state)
        assert "监控异常" in prompt
        assert "CPU_HIGH" in prompt
        assert "High CPU usage" in prompt

    def test_prompt_generate_empty_data(self):
        """Test prompt generation with empty data"""
        agent = InspectAgent({})
        
        # Create a mock state with empty data
        state = InspectState(
            machine_id="unknown",
            start_time="2025-07-28 00:00:00",
            end_time="2025-07-30 23:59:59",
            machine_id_type=MachineIdType.UNKNOWN,
            tool_map={},
            info_data={},
            finished=False,
            recommendations=[],
            overview="",
        )
        
        prompt = agent._prompt_generate(state)
        assert "机器类型未知" in prompt


# Test data that was originally in the _main function of inspect.py
# These are kept as test data constants for reference
TEST_CASES = [
    {
        "machine_id": "i-bp1fz27ong6p6w693vn5",
        "description": "ECS Instance ID 测试",
        "start_time": "2025-07-28 00:00:00",
        "end_time": "2025-07-30 23:59:59",
    },
    {
        "machine_id": "i-wz97reb1ywapz0c4luac",
        "description": "ECS Instance ID 测试 2",
        "start_time": "2025-07-28 00:00:00",
        "end_time": "2025-07-30 23:59:59",
    },
    {
        "machine_id": "***********",
        "description": "NC IP Address 测试",
        "start_time": "2025-07-28 00:00:00",
        "end_time": "2025-07-30 23:59:59",
    },
]


class TestInspectAgentTestData:
    """Test data validation for InspectAgent"""

    def test_test_cases_exist(self):
        """Test that test cases are defined"""
        assert len(TEST_CASES) == 3
        assert TEST_CASES[0]["machine_id"] == "i-bp1fz27ong6p6w693vn5"
        assert TEST_CASES[1]["machine_id"] == "i-wz97reb1ywapz0c4luac"
        assert TEST_CASES[2]["machine_id"] == "***********"

    def test_test_case_structure(self):
        """Test that test cases have the correct structure"""
        for i, test_case in enumerate(TEST_CASES):
            assert "machine_id" in test_case
            assert "description" in test_case
            assert "start_time" in test_case
            assert "end_time" in test_case
            assert test_case["start_time"] == "2025-07-28 00:00:00"
            assert test_case["end_time"] == "2025-07-30 23:59:59"


if __name__ == "__main__":
    pytest.main([__file__])