"""
Unit tests for PGVectorSOPStore - Real database integration tests.

These tests connect to the actual PostgreSQL database and test real functionality
without mocks. They test the core methods: store_sops, load_and_store_all_sops,
get_all_sops, get_sop_by_id, and search_sops.
"""

import pytest
import asyncio
from datetime import datetime
from typing import List

from deep_diagnose.core.reasoning.planning.storage.pg_vector import (
    PGVectorSOPStore, 
    SOPDocument, 
    SOPLoader
)
from deep_diagnose.core.reasoning.planning.models import SOP


class TestPGVectorSOPStore:
    """Test class for PGVectorSOPStore with real database integration."""
    
    @pytest.fixture(scope="class")
    def event_loop(self):
        """Create an event loop for the test class."""
        loop = asyncio.new_event_loop()
        yield loop
        loop.close()
    
    @pytest.fixture(scope="class")
    async def sop_store(self):
        """Create a PGVectorSOPStore instance for testing."""
        # Use default configuration from config files
        store = PGVectorSOPStore(collection_name="test_sops")
        yield store
        
        # Cleanup: Clear test data after tests
        try:
            # Note: In a real implementation, you might want to add a cleanup method
            # For now, we'll rely on the test collection name to isolate test data
            pass
        except Exception as e:
            print(f"Cleanup warning: {e}")
    
    @pytest.fixture
    def sample_sop_documents(self) -> List[SOPDocument]:
        """Create sample SOP documents for testing."""
        now = datetime.now()
        
        return [
            SOPDocument(
                sop_id="test_instance_restart",
                name="实例重启诊断",
                scenario="ECS实例异常重启故障诊断",
                template_file_path="instance/diagnosis/instance_restart_single.md",
                content="# 实例重启诊断\n\n这是一个测试SOP文档，用于诊断ECS实例重启问题。\n\n## 诊断步骤\n1. 检查实例状态\n2. 查看系统日志\n3. 分析重启原因",
                example_user_queries=[
                    "我的ECS实例突然重启了，怎么回事？",
                    "实例为什么会自动重启？",
                    "如何诊断实例重启问题？"
                ],
                tool_dependencies=["getVmBasicInfo", "listMonitorExceptions"],
                category="instance",
                subcategory="diagnosis",
                file_path="/test/path/instance_restart_single.md",
                created_at=now,
                updated_at=now,
                content_hash="test_hash_1"
            ),
            SOPDocument(
                sop_id="test_nc_crash_analysis",
                name="NC宿主机崩溃分析",
                scenario="NC宿主机崩溃故障分析",
                template_file_path="nc/diagnosis/nc_host_crash_analysis.md",
                content="# NC宿主机崩溃分析\n\n这是一个测试SOP文档，用于分析NC宿主机崩溃问题。\n\n## 分析步骤\n1. 检查宿主机状态\n2. 分析崩溃日志\n3. 确定影响范围",
                example_user_queries=[
                    "NC宿主机崩溃了怎么办？",
                    "如何分析宿主机崩溃原因？",
                    "宿主机崩溃影响哪些实例？"
                ],
                tool_dependencies=["getNcBasicInfo", "listVmsOnNc"],
                category="nc",
                subcategory="diagnosis",
                file_path="/test/path/nc_host_crash_analysis.md",
                created_at=now,
                updated_at=now,
                content_hash="test_hash_2"
            ),
            SOPDocument(
                sop_id="test_customer_stability",
                name="客户稳定性报告",
                scenario="生成客户稳定性分析报告",
                template_file_path="customer/reporting/customer_stability_report.md",
                content="# 客户稳定性报告\n\n这是一个测试SOP文档，用于生成客户稳定性报告。\n\n## 报告内容\n1. 实例运行状态\n2. 故障统计\n3. 稳定性评估",
                example_user_queries=[
                    "生成客户稳定性报告",
                    "客户的实例稳定性如何？",
                    "分析客户的故障情况"
                ],
                tool_dependencies=["getUserInfo", "listMonitorExceptions"],
                category="customer",
                subcategory="reporting",
                file_path="/test/path/customer_stability_report.md",
                created_at=now,
                updated_at=now,
                content_hash="test_hash_3"
            )
        ]
    
    @pytest.mark.asyncio
    async def test_store_sops(self, sop_store: PGVectorSOPStore, sample_sop_documents: List[SOPDocument]):
        """Test storing SOP documents in the vector database."""
        # Store the sample SOPs
        result = await sop_store.store_sops(sample_sop_documents)
        
        # Verify storage was successful
        assert result is True, "Failed to store SOP documents"
        
        # Verify we can retrieve the stored documents
        all_sops = await sop_store.get_all_sops()
        assert len(all_sops) >= len(sample_sop_documents), "Not all SOPs were stored"
        
        # Verify the stored SOPs contain our test data
        stored_sop_ids = {sop.sop_id for sop in all_sops}
        expected_sop_ids = {doc.sop_id for doc in sample_sop_documents}
        
        for expected_id in expected_sop_ids:
            assert expected_id in stored_sop_ids, f"SOP {expected_id} was not found in stored SOPs"
    
    @pytest.mark.asyncio
    async def test_load_and_store_all_sops(self, sop_store: PGVectorSOPStore):
        """Test loading SOPs from files and storing them in the database."""
        # This test loads real SOP files from the project
        result = await sop_store.load_and_store_all_sops()
        
        # The result depends on whether SOP files exist in the project
        # If no files are found, it should return False, otherwise True
        if result:
            # If SOPs were loaded and stored, verify we can retrieve them
            all_sops = await sop_store.get_all_sops()
            assert len(all_sops) > 0, "No SOPs were loaded and stored"
            
            # Verify each SOP has required fields
            for sop in all_sops:
                assert sop.sop_id, "SOP missing sop_id"
                assert sop.name, "SOP missing name"
                assert sop.content, "SOP missing content"
        else:
            # If no SOPs were found, that's also a valid result for testing
            print("No SOP files found to load - this is expected in test environment")
    
    @pytest.mark.asyncio
    async def test_get_all_sops(self, sop_store: PGVectorSOPStore, sample_sop_documents: List[SOPDocument]):
        """Test retrieving all SOPs from the database."""
        # First store some test data
        await sop_store.store_sops(sample_sop_documents)
        
        # Retrieve all SOPs
        all_sops = await sop_store.get_all_sops()
        
        # Verify we got results
        assert isinstance(all_sops, list), "get_all_sops should return a list"
        assert len(all_sops) >= len(sample_sop_documents), "Should retrieve at least the stored SOPs"
        
        # Verify each SOP has the expected structure
        for sop in all_sops:
            assert isinstance(sop, SOP), "Each result should be a SOP instance"
            assert sop.sop_id, "Each SOP should have an sop_id"
            assert sop.name, "Each SOP should have a name"
            assert sop.content, "Each SOP should have content"
        
        # Verify our test SOPs are included
        retrieved_sop_ids = {sop.sop_id for sop in all_sops}
        for test_doc in sample_sop_documents:
            assert test_doc.sop_id in retrieved_sop_ids, f"Test SOP {test_doc.sop_id} not found in results"
    
    @pytest.mark.asyncio
    async def test_get_sop_by_id(self, sop_store: PGVectorSOPStore, sample_sop_documents: List[SOPDocument]):
        """Test retrieving a specific SOP by its ID."""
        # Store test data
        await sop_store.store_sops(sample_sop_documents)
        
        # Test retrieving existing SOPs
        for test_doc in sample_sop_documents:
            sop = await sop_store.get_sop_by_id(test_doc.sop_id)
            
            assert sop is not None, f"SOP {test_doc.sop_id} should be found"
            assert isinstance(sop, SOP), "Result should be a SOP instance"
            assert sop.sop_id == test_doc.sop_id, "Retrieved SOP should have correct ID"
            assert sop.name == test_doc.name, "Retrieved SOP should have correct name"
            assert sop.scenario == test_doc.scenario, "Retrieved SOP should have correct scenario"
            assert test_doc.content in sop.content, "Retrieved SOP should contain the original content"
        
        # Test retrieving non-existent SOP
        non_existent_sop = await sop_store.get_sop_by_id("non_existent_sop_id")
        assert non_existent_sop is None, "Non-existent SOP should return None"
    
    @pytest.mark.asyncio
    async def test_search_sops(self, sop_store: PGVectorSOPStore, sample_sop_documents: List[SOPDocument]):
        """Test searching SOPs using semantic similarity."""
        # Store test data
        await sop_store.store_sops(sample_sop_documents)
        
        # Test search with different queries
        test_queries = [
            ("实例重启", "test_instance_restart"),  # Should find instance restart SOP
            ("宿主机崩溃", "test_nc_crash_analysis"),  # Should find NC crash analysis SOP
            ("稳定性报告", "test_customer_stability"),  # Should find customer stability SOP
            ("ECS故障诊断", None),  # General query, should find multiple results
        ]
        
        for query, expected_sop_id in test_queries:
            results = await sop_store.search_sops(query, k=5)
            
            assert isinstance(results, list), f"Search results for '{query}' should be a list"
            assert len(results) > 0, f"Search for '{query}' should return results"
            
            # Verify result structure
            for sop, score in results:
                assert isinstance(sop, SOP), "Each result should contain a SOP instance"
                assert isinstance(score, (int, float)), "Each result should contain a numeric score"
                assert sop.sop_id, "Each SOP should have an sop_id"
                assert sop.content, "Each SOP should have content"
            
            # If we expect a specific SOP, verify it's in the results
            if expected_sop_id:
                found_sop_ids = {sop.sop_id for sop, score in results}
                assert expected_sop_id in found_sop_ids, f"Expected SOP {expected_sop_id} not found in search results for '{query}'"
        
        # Test search with category filter
        instance_results = await sop_store.search_sops("诊断", k=10, category="instance")
        assert isinstance(instance_results, list), "Category filtered search should return a list"
        
        # Test search with category and subcategory filter
        diagnosis_results = await sop_store.search_sops("分析", k=10, category="nc", subcategory="diagnosis")
        assert isinstance(diagnosis_results, list), "Category and subcategory filtered search should return a list"
    
    @pytest.mark.asyncio
    async def test_search_sops_empty_query(self, sop_store: PGVectorSOPStore, sample_sop_documents: List[SOPDocument]):
        """Test search behavior with empty or invalid queries."""
        # Store test data
        await sop_store.store_sops(sample_sop_documents)
        
        # Test empty query
        empty_results = await sop_store.search_sops("", k=5)
        assert isinstance(empty_results, list), "Empty query should return a list"
        
        # Test very specific query that might not match
        specific_results = await sop_store.search_sops("非常特殊的查询内容12345", k=5)
        assert isinstance(specific_results, list), "Specific query should return a list"
    
    @pytest.mark.asyncio
    async def test_real_database_connection(self, sop_store: PGVectorSOPStore):
        """Test that we can actually connect to the real database."""
        # This test verifies the database connection is working
        try:
            # Try a simple operation to verify connection
            all_sops = await sop_store.get_all_sops()
            assert isinstance(all_sops, list), "Database connection should work and return a list"
            print(f"Successfully connected to database. Found {len(all_sops)} existing SOPs.")
        except Exception as e:
            pytest.fail(f"Failed to connect to database: {e}")
    
    @pytest.mark.asyncio
    async def test_sop_content_integrity(self, sop_store: PGVectorSOPStore, sample_sop_documents: List[SOPDocument]):
        """Test that SOP content is stored and retrieved correctly."""
        # Store test data
        await sop_store.store_sops(sample_sop_documents)
        
        # Retrieve and verify content integrity
        for test_doc in sample_sop_documents:
            retrieved_sop = await sop_store.get_sop_by_id(test_doc.sop_id)
            assert retrieved_sop is not None, f"SOP {test_doc.sop_id} should be retrievable"
            
            # Verify content contains key information
            assert test_doc.name in retrieved_sop.content or retrieved_sop.name == test_doc.name, "SOP name should be preserved"
            assert test_doc.scenario in retrieved_sop.content or retrieved_sop.scenario == test_doc.scenario, "SOP scenario should be preserved"
            
            # Verify example queries are preserved
            assert retrieved_sop.example_user_queries == test_doc.example_user_queries, "Example queries should be preserved"
            
            # Verify tool dependencies are preserved
            assert retrieved_sop.tool_dependencies == test_doc.tool_dependencies, "Tool dependencies should be preserved"


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])