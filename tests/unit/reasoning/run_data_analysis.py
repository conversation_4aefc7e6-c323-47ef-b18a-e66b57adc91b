import warnings
from datetime import datetime
from pathlib import Path

import numpy as np
import pandas as pd

warnings.filterwarnings('ignore')

# 创建输出目录
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
output_dir = Path(f"analysis_results_{timestamp}")
output_dir.mkdir(exist_ok=True)
charts_dir = output_dir / "charts"
charts_dir.mkdir(exist_ok=True)
data_dir = output_dir / "data"
data_dir.mkdir(exist_ok=True)

print(f"📁 创建输出目录: {output_dir}")

# 生成示例年龄数据
np.random.seed(42)
# 创建符合现实分布的年龄数据（正态分布偏移）
ages = np.concatenate([
    np.random.normal(25, 8, 500),  # 年轻人较多
    np.random.normal(45, 10, 300), # 中年人
    np.random.normal(65, 7, 100),  # 老年人较少
])
ages = np.clip(ages, 1, 100)  # 限制在1-100岁之间
ages = ages.astype(int)  # 转换为整数

# 创建DataFrame
df = pd.DataFrame({'age': ages})
print(f"📊 成功生成年龄数据，共 {len(df)} 条记录")

# 保存原始数据
data_path = data_dir / "age_data.csv"
df.to_csv(data_path, index=False)
print(f"💾 原始数据已保存至: {data_path}")