#!/usr/bin/env python3
"""
FVT验证脚本

验证FVT模块是否可以正常导入和使用。
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_fvt_imports():
    """测试FVT模块导入"""
    print("🔍 测试FVT模块导入...")
    
    try:
        # 测试基础导入
        from deep_diagnose.core.fvt import FVTAgent, FVTState, FVTAgentEvent
        print("✅ 基础模块导入成功")
        
        # 测试AgentFactory注册
        from deep_diagnose.core.agent.agent_factory import AgentFactory
        agents = AgentFactory.list_available_agents()
        print(f"✅ 可用的智能体: {agents}")
        
        if 'FVTAgent' in agents:
            print("✅ FVTAgent已成功注册")
        else:
            print("❌ FVTAgent未注册")
            return False
            
        # 测试创建FVTAgent实例
        fvt_agent = AgentFactory.create_agent('FVTAgent')
        print(f"✅ FVTAgent实例创建成功: {type(fvt_agent).__name__}")
        
        # 测试FVTState
        from deep_diagnose.core.fvt.workflow.types import FVTState
        from langchain_core.messages import HumanMessage
        
        state = FVTState(
            messages=[HumanMessage(content="测试消息")],
            observations=["测试观察"]
        )
        print("✅ FVTState创建成功")
        
        # 测试FVTAgentEvent
        event = FVTAgentEvent(request_id="test_123")
        event.understanding = "测试理解"
        event.result = "测试结果"
        print("✅ FVTAgentEvent创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 开始FVT模块验证")
    print("=" * 50)
    
    success = test_fvt_imports()
    
    print("=" * 50)
    if success:
        print("🎉 FVT模块验证成功!")
        print("\n📋 创建的组件:")
        print("  • FVTAgent - 主智能体类")
        print("  • FVTState - 状态模型")
        print("  • FVTAgentEvent - 事件模型")  
        print("  • fvt_agent_node - 子智能体节点")
        print("  • workflow - 工作流(start→fvt→end)")
        
        print("\n🔧 功能特性:")
        print("  • 基于LangGraph的ReactAgent架构")
        print("  • 包含messages和observations字段的状态管理")
        print("  • 流式事件处理")
        print("  • 在AgentFactory中注册，支持统一创建")
        
    else:
        print("❌ FVT模块验证失败")
        return 1
        
    return 0

if __name__ == "__main__":
    exit(main())